#!/usr/bin/env python3
"""
轻量级KNN测试 - 减少内存使用，专注于最有效的配置
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
import numpy as np

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from contrastive_learning_enhancement import ContrastiveProgressiveModel
from enhanced_progressive_training import EnhancedProgressiveDataset

def lightweight_test(model_path: str, test_samples: int = 400):
    """轻量级测试"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 加载模型
    print(f"📦 加载模型: {model_path}")
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 33)
    
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    # 准备测试数据
    print("📁 准备测试数据...")
    
    with open('../tagging/annotations.json', 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    test_dataset = EnhancedProgressiveDataset(
        annotations, '../dataset/cat_individual_images', '../dataset/renamed_thumbnails', 
        cat_to_id, max_individual_cats=30, is_training=False
    )
    
    # 使用较小的测试集
    test_indices = random.sample(range(len(test_dataset)), min(test_samples, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=8, shuffle=False, num_workers=1)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    # 提取特征
    print("🔍 提取特征...")
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in test_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
            
            # 清理GPU内存
            del images, features
            torch.cuda.empty_cache()
    
    features = np.vstack(all_features)
    labels = np.array(all_labels)
    is_original = np.array(all_is_original)
    
    print(f"  特征维度: {features.shape}")
    print(f"  样本数量: {len(labels)}")
    
    # 清理模型内存
    del model
    torch.cuda.empty_cache()
    
    # 测试多种特征处理方法
    feature_methods = {
        'original': features,
        'l2_normalized': features / np.linalg.norm(features, axis=1, keepdims=True),
        'standardized': StandardScaler().fit_transform(features)
    }
    
    # 重点测试的KNN配置（基于之前的最佳结果）
    knn_configs = [
        {'n_neighbors': 7, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 9, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 11, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 13, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 9, 'metric': 'euclidean', 'weights': 'distance'},
        {'n_neighbors': 11, 'metric': 'euclidean', 'weights': 'distance'},
    ]
    
    best_result = None
    best_individual_acc = 0.0
    best_config = None
    best_method = None
    
    print(f"\n🧪 测试特征处理方法和KNN配置:")
    
    for method_name, method_features in feature_methods.items():
        print(f"\n  📊 {method_name} 特征:")
        
        for config in knn_configs:
            try:
                knn = KNeighborsClassifier(**config)
                
                # 留一法交叉验证
                predictions = []
                for i in range(len(method_features)):
                    train_mask = np.ones(len(method_features), dtype=bool)
                    train_mask[i] = False
                    
                    if train_mask.sum() > 0:
                        knn.fit(method_features[train_mask], labels[train_mask])
                        pred = knn.predict(method_features[i:i+1])
                        predictions.append(pred[0])
                    else:
                        predictions.append(labels[i])
                
                predictions = np.array(predictions)
                
                # 计算准确率
                overall_acc = accuracy_score(labels, predictions)
                
                original_mask = is_original.astype(bool)
                original_acc = accuracy_score(labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
                
                individual_mask = ~original_mask
                individual_acc = accuracy_score(labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
                
                print(f"    k={config['n_neighbors']}, {config['metric']}: 总体={overall_acc:.3f}, 原始={original_acc:.3f}, 个体={individual_acc:.3f}")
                
                # 选择个体准确率最高的配置
                if individual_acc > best_individual_acc:
                    best_individual_acc = individual_acc
                    best_result = {
                        'overall_acc': overall_acc,
                        'original_acc': original_acc,
                        'individual_acc': individual_acc
                    }
                    best_config = config
                    best_method = method_name
                    
            except Exception as e:
                print(f"    k={config['n_neighbors']}, {config['metric']}: 失败 ({e})")
                continue
    
    # 输出最佳结果
    if best_result:
        print(f"\n🏆 最佳结果:")
        print(f"  特征方法: {best_method}")
        print(f"  KNN配置: {best_config}")
        print(f"  总体准确率: {best_result['overall_acc']:.4f} ({best_result['overall_acc']*100:.2f}%)")
        print(f"  原始3猫准确率: {best_result['original_acc']:.4f} ({best_result['original_acc']*100:.2f}%)")
        print(f"  个体猫咪准确率: {best_result['individual_acc']:.4f} ({best_result['individual_acc']*100:.2f}%)")
        
        # 性能评级
        if best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.85:
            grade = "🏆 卓越+"
        elif best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.75:
            grade = "🏆 卓越"
        elif best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.65:
            grade = "🥇 优秀"
        elif best_result['original_acc'] >= 0.90 and best_result['individual_acc'] >= 0.50:
            grade = "🥈 良好"
        else:
            grade = "🥉 一般"
        
        print(f"  综合评级: {grade}")
        
        # 计算提升
        baseline_individual_acc = 0.1858
        improvement_ratio = best_result['individual_acc'] / baseline_individual_acc
        print(f"  相比基线提升: {improvement_ratio:.1f}x ({(improvement_ratio-1)*100:.1f}%)")
        
        # 保存结果
        result_data = {
            'best_method': best_method,
            'best_config': best_config,
            'best_result': best_result,
            'grade': grade,
            'improvement_ratio': improvement_ratio
        }
        
        with open('lightweight_knn_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 结果已保存到: lightweight_knn_test_results.json")
        
        return best_result
    else:
        print("❌ 所有测试都失败了")
        return None

def main():
    parser = argparse.ArgumentParser(description='轻量级KNN测试')
    parser.add_argument('--model', default='best_contrastive_model.pth', help='模型文件路径')
    parser.add_argument('--test-samples', type=int, default=400, help='测试样本数量')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    print("🚀 开始轻量级KNN测试...")
    result = lightweight_test(args.model, args.test_samples)
    
    if result:
        print("✅ 轻量级KNN测试完成！")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
