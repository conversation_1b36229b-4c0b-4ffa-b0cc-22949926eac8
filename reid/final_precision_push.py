#!/usr/bin/env python3
"""
最终精度冲刺 - 基于现有最佳模型追求99.5%
使用测试时增强、集成学习、精细调优等方法

策略:
1. 基于现有最佳模型进行精细调优
2. 测试时增强 (TTA)
3. 多模型集成
4. 困难样本重训练
5. 伪标签学习
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.optim.lr_scheduler import CosineAnnealingLR
from torchvision import transforms
from PIL import Image

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from few_shot_meta_learning import PrototypicalNetwork, FewShotDataset, meta_train_epoch, meta_evaluate
from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TTAEvaluator:
    """测试时增强评估器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
        # 定义多种TTA变换
        self.tta_transforms = [
            # 原始
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 水平翻转
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 中心裁剪
            transforms.Compose([
                transforms.Resize((240, 240)),
                transforms.CenterCrop((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 轻微旋转
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomRotation(5),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 亮度调整
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ColorJitter(brightness=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
    
    def tta_predict(self, images):
        """使用TTA进行预测"""
        self.model.eval()
        
        all_features = []
        
        with torch.no_grad():
            for transform in self.tta_transforms:
                # 应用变换
                if isinstance(images, torch.Tensor):
                    # 如果输入是tensor，先转换为PIL再应用变换
                    pil_images = [transforms.ToPILImage()(img.cpu()) for img in images]
                    transformed_images = torch.stack([transform(img) for img in pil_images])
                else:
                    transformed_images = torch.stack([transform(img) for img in images])
                
                transformed_images = transformed_images.to(self.device)
                
                # 提取特征
                features = self.model(transformed_images)
                all_features.append(features)
        
        # 平均所有TTA结果
        avg_features = torch.stack(all_features).mean(dim=0)
        return avg_features
    
    def evaluate_with_tta(self, dataset, num_episodes=50):
        """使用TTA评估模型"""
        total_accuracy = 0.0
        original_correct = 0
        individual_correct = 0
        original_total = 0
        individual_total = 0
        
        for episode in range(num_episodes):
            # 采样episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(10, len(dataset.classes)), k_shot=2, q_query=8
            )
            
            # TTA for support and query
            support_features = self.tta_predict(support_images)
            query_features = self.tta_predict(query_images)
            
            support_labels = support_labels.to(self.device)
            query_labels = query_labels.to(self.device)
            
            # 计算原型并分类
            prototypes, prototype_labels = self.model.compute_prototypes(support_features, support_labels)
            predictions, _ = self.model.classify_by_prototypes(query_features, prototypes, prototype_labels)
            
            # 统计准确率
            accuracy = (predictions == query_labels).float().mean()
            total_accuracy += accuracy.item()
            
            # 分别统计原始3猫和个体猫
            for j, label in enumerate(query_labels):
                if label.item() < 3:  # 原始3猫
                    original_total += 1
                    if predictions[j] == label:
                        original_correct += 1
                else:  # 个体猫
                    individual_total += 1
                    if predictions[j] == label:
                        individual_correct += 1
        
        avg_accuracy = total_accuracy / num_episodes
        original_accuracy = original_correct / original_total if original_total > 0 else 0
        individual_accuracy = individual_correct / individual_total if individual_total > 0 else 0
        
        return avg_accuracy, original_accuracy, individual_accuracy

class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, model_paths, device):
        self.device = device
        self.models = []
        self.tta_evaluators = []
        
        # 加载所有模型
        for model_path in model_paths:
            if os.path.exists(model_path):
                logger.info(f"加载模型: {model_path}")
                
                # 加载模型
                checkpoint = torch.load(model_path, map_location=device, weights_only=False)
                feature_dim = checkpoint.get('feature_dim', 2048)
                
                base_model = FeatureExtractorModel(feature_dim=feature_dim)
                model = PrototypicalNetwork(base_model, feature_dim=feature_dim)
                model.load_state_dict(checkpoint['model_state_dict'])
                model.to(device)
                model.eval()
                
                self.models.append(model)
                self.tta_evaluators.append(TTAEvaluator(model, device))
                
                logger.info(f"✅ 模型加载成功，准确率: {checkpoint.get('accuracy', 'N/A')}")
    
    def ensemble_evaluate(self, dataset, num_episodes=30):
        """集成评估"""
        if not self.models:
            logger.error("没有可用的模型")
            return 0.0, 0.0, 0.0
        
        logger.info(f"使用 {len(self.models)} 个模型进行集成评估...")
        
        total_accuracy = 0.0
        original_correct = 0
        individual_correct = 0
        original_total = 0
        individual_total = 0
        
        for episode in range(num_episodes):
            # 采样episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(8, len(dataset.classes)), k_shot=3, q_query=6
            )
            
            support_labels = support_labels.to(self.device)
            query_labels = query_labels.to(self.device)
            
            # 收集所有模型的预测
            all_predictions = []
            
            for i, tta_evaluator in enumerate(self.tta_evaluators):
                # 使用TTA获取特征
                support_features = tta_evaluator.tta_predict(support_images)
                query_features = tta_evaluator.tta_predict(query_images)
                
                # 计算原型并分类
                prototypes, prototype_labels = tta_evaluator.model.compute_prototypes(support_features, support_labels)
                predictions, confidences = tta_evaluator.model.classify_by_prototypes(query_features, prototypes, prototype_labels)
                
                all_predictions.append(predictions)
            
            # 集成预测 - 投票
            ensemble_predictions = []
            for i in range(len(query_labels)):
                votes = [pred[i].item() for pred in all_predictions]
                # 简单多数投票
                ensemble_pred = max(set(votes), key=votes.count)
                ensemble_predictions.append(ensemble_pred)
            
            ensemble_predictions = torch.tensor(ensemble_predictions, device=self.device)
            
            # 统计准确率
            accuracy = (ensemble_predictions == query_labels).float().mean()
            total_accuracy += accuracy.item()
            
            # 分别统计原始3猫和个体猫
            for j, label in enumerate(query_labels):
                if label.item() < 3:  # 原始3猫
                    original_total += 1
                    if ensemble_predictions[j] == label:
                        original_correct += 1
                else:  # 个体猫
                    individual_total += 1
                    if ensemble_predictions[j] == label:
                        individual_correct += 1
        
        avg_accuracy = total_accuracy / num_episodes
        original_accuracy = original_correct / original_total if original_total > 0 else 0
        individual_accuracy = individual_correct / individual_total if individual_total > 0 else 0
        
        return avg_accuracy, original_accuracy, individual_accuracy

def fine_tune_model(model, dataset, device, epochs=20):
    """精细调优模型"""
    logger.info("开始精细调优...")
    
    model.train()
    optimizer = optim.AdamW(model.parameters(), lr=1e-6, weight_decay=1e-5)
    scheduler = CosineAnnealingLR(optimizer, T_max=epochs, eta_min=1e-8)
    
    best_accuracy = 0.0
    
    for epoch in range(epochs):
        total_loss = 0.0
        total_accuracy = 0.0
        episodes_per_epoch = 100
        
        for episode in range(episodes_per_epoch):
            # 采样episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(8, len(dataset.classes)), k_shot=3, q_query=6
            )
            
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)
            
            # 前向传播
            support_features = model(support_images)
            query_features = model(query_images)
            
            # 计算原型
            prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
            
            # 计算距离和损失
            distances = torch.cdist(query_features, prototypes, p=2)
            logits = -distances
            
            # 使用标签平滑
            loss = F.cross_entropy(logits, query_labels, label_smoothing=0.05)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            optimizer.step()
            
            # 统计
            predictions = logits.argmin(dim=1)
            accuracy = (predictions == query_labels).float().mean()
            
            total_loss += loss.item()
            total_accuracy += accuracy.item()
        
        scheduler.step()
        
        avg_loss = total_loss / episodes_per_epoch
        avg_accuracy = total_accuracy / episodes_per_epoch
        
        logger.info(f"精调 Epoch {epoch + 1}: 损失={avg_loss:.4f}, 准确率={avg_accuracy:.4f}")
        
        # 每5轮评估一次
        if (epoch + 1) % 5 == 0:
            val_acc, _, _ = meta_evaluate(model, dataset, device, num_episodes=20)
            logger.info(f"  验证准确率: {val_acc:.4f}")
            
            if val_acc > best_accuracy:
                best_accuracy = val_acc
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'feature_dim': 2048,
                    'accuracy': val_acc
                }, 'fine_tuned_model.pth')
                logger.info(f"  ✅ 保存精调模型 (准确率: {val_acc:.4f})")
    
    return best_accuracy

def main():
    parser = argparse.ArgumentParser(description='最终精度冲刺')
    parser.add_argument('--annotations', required=True, help='标注文件路径')
    parser.add_argument('--individual-data-dir', required=True, help='个体猫数据目录')
    parser.add_argument('--original-dir', required=True, help='原始猫数据目录')
    parser.add_argument('--models', nargs='+', required=True, help='模型文件路径列表')
    parser.add_argument('--fine-tune', action='store_true', help='是否进行精细调优')
    
    args = parser.parse_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载数据
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 创建cat_to_id映射
    original_cats = {'小白': 0, '小花': 1, '小黑': 2}
    cat_to_id = original_cats.copy()
    
    individual_dirs = [d for d in os.listdir(args.individual_data_dir) 
                      if os.path.isdir(os.path.join(args.individual_data_dir, d))]
    for i, cat_dir in enumerate(sorted(individual_dirs)):
        if cat_dir not in cat_to_id:
            cat_to_id[cat_dir] = len(cat_to_id)
    
    # 创建数据集
    dataset = FewShotDataset(annotations, args.individual_data_dir, 
                           args.original_dir, cat_to_id, max_individual_cats=50)
    
    logger.info(f"数据集: {len(cat_to_id)} 个类别")
    
    # 精细调优（如果需要）
    if args.fine_tune and len(args.models) > 0:
        logger.info("🔧 开始精细调优最佳模型...")
        
        # 加载最佳模型
        best_model_path = args.models[0]
        checkpoint = torch.load(best_model_path, map_location=device, weights_only=False)
        feature_dim = checkpoint.get('feature_dim', 2048)
        
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        model = PrototypicalNetwork(base_model, feature_dim=feature_dim)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        
        fine_tune_accuracy = fine_tune_model(model, dataset, device)
        logger.info(f"精细调优完成，最佳准确率: {fine_tune_accuracy:.4f}")
        
        # 将精调模型添加到模型列表
        if os.path.exists('fine_tuned_model.pth'):
            args.models.append('fine_tuned_model.pth')
    
    # 创建集成预测器
    ensemble = EnsemblePredictor(args.models, device)
    
    # 集成评估
    logger.info("🚀 开始集成评估...")
    ensemble_acc, original_acc, individual_acc = ensemble.ensemble_evaluate(dataset, num_episodes=100)
    
    logger.info(f"\n🏆 最终集成结果:")
    logger.info(f"   总体准确率: {ensemble_acc:.4f} ({ensemble_acc*100:.2f}%)")
    logger.info(f"   原始3猫准确率: {original_acc:.4f} ({original_acc*100:.2f}%)")
    logger.info(f"   个体猫准确率: {individual_acc:.4f} ({individual_acc*100:.2f}%)")
    
    # 检查是否达到目标
    target_accuracy = 0.995
    if ensemble_acc >= target_accuracy:
        logger.info(f"🎉🎉🎉 成功达到目标准确率 {target_accuracy:.1%}!")
    else:
        gap = (target_accuracy - ensemble_acc) * 100
        logger.info(f"📈 距离目标还差 {gap:.2f} 个百分点")
        
        if gap < 1.0:
            logger.info("💡 建议: 距离目标很近，可以尝试更多的TTA变换或更大的集成")
        elif gap < 2.0:
            logger.info("💡 建议: 可以尝试收集更多高质量数据或使用更强的模型架构")
        else:
            logger.info("💡 建议: 需要更根本的改进，如更好的特征提取或数据质量提升")

if __name__ == "__main__":
    main()
