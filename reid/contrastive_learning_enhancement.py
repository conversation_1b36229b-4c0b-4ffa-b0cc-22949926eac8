#!/usr/bin/env python3
"""
对比学习增强版 - 通过对比学习机制显著提升特征区分度
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContrastiveLoss(nn.Module):
    """对比损失函数"""
    
    def __init__(self, temperature=0.1):
        super(ContrastiveLoss, self).__init__()
        self.temperature = temperature
        
    def forward(self, features, labels):
        """
        features: [batch_size, feature_dim]
        labels: [batch_size]
        """
        batch_size = features.shape[0]
        
        # 归一化特征
        features = F.normalize(features, dim=1)
        
        # 计算相似度矩阵
        similarity_matrix = torch.matmul(features, features.T) / self.temperature
        
        # 创建标签掩码
        labels = labels.contiguous().view(-1, 1)
        mask = torch.eq(labels, labels.T).float().to(features.device)
        
        # 移除对角线（自己与自己的相似度）
        mask = mask - torch.eye(batch_size).to(features.device)
        
        # 计算正样本和负样本的logits
        exp_sim = torch.exp(similarity_matrix)
        
        # 正样本：同类别的样本
        pos_sim = exp_sim * mask
        
        # 负样本：不同类别的样本
        neg_sim = exp_sim * (1 - mask - torch.eye(batch_size).to(features.device))
        
        # 计算对比损失
        pos_sum = pos_sim.sum(dim=1)
        neg_sum = neg_sim.sum(dim=1)
        
        # 避免除零
        pos_sum = torch.clamp(pos_sum, min=1e-8)
        total_sum = pos_sum + neg_sum
        
        loss = -torch.log(pos_sum / total_sum)
        
        # 只对有正样本的位置计算损失
        valid_mask = (mask.sum(dim=1) > 0)
        if valid_mask.sum() > 0:
            loss = loss[valid_mask].mean()
        else:
            loss = torch.tensor(0.0, device=features.device, requires_grad=True)
        
        return loss

class ContrastiveProgressiveModel(nn.Module):
    """对比学习增强的渐进式模型"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=53):
        super(ContrastiveProgressiveModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        # 部分解冻backbone的最后几层
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 解冻最后一个stage
        if hasattr(self.feature_extractor.backbone, 'layers'):
            for param in self.feature_extractor.backbone.layers[-1].parameters():
                param.requires_grad = True
        
        # 增强的特征提取层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 对比学习投影头
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Linear(feature_dim // 2, 256)  # 对比学习特征维度
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False, return_projections=False):
        # 特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.feature_enhancer(base_features)
        
        if return_features:
            return enhanced_features
        
        # 对比学习投影
        projections = self.projection_head(enhanced_features)
        
        if return_projections:
            return projections
        
        # 分类
        logits = self.classifier(enhanced_features)
        
        return logits, enhanced_features, projections

def contrastive_train_epoch(model, train_loader, optimizer, device, epoch, total_epochs, 
                           contrastive_criterion, classification_criterion, 
                           contrastive_weight=0.5, original_weight=2.0):
    """对比学习训练一个epoch"""
    
    model.train()
    
    total_loss = 0.0
    contrastive_loss_sum = 0.0
    classification_loss_sum = 0.0
    num_batches = 0
    
    for images, labels, categories, is_original in train_loader:
        images = images.to(device)
        labels = labels.to(device)
        is_original = is_original.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        logits, features, projections = model(images)
        
        # 分类损失
        classification_losses = classification_criterion(logits, labels)
        weights = torch.where(is_original, original_weight, 1.0)
        weighted_classification_loss = (classification_losses * weights).mean()
        
        # 对比学习损失
        contrastive_loss = contrastive_criterion(projections, labels)
        
        # 总损失
        total_loss_batch = weighted_classification_loss + contrastive_weight * contrastive_loss
        
        # 特征正则化
        feature_reg = 0.001 * torch.norm(features, p=2, dim=1).mean()
        total_loss_batch += feature_reg
        
        total_loss_batch.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += total_loss_batch.item()
        contrastive_loss_sum += contrastive_loss.item()
        classification_loss_sum += weighted_classification_loss.item()
        num_batches += 1
    
    avg_total_loss = total_loss / num_batches
    avg_contrastive_loss = contrastive_loss_sum / num_batches
    avg_classification_loss = classification_loss_sum / num_batches
    
    logger.info(f"Epoch {epoch+1}/{total_epochs}")
    logger.info(f"  总损失: {avg_total_loss:.4f}")
    logger.info(f"  分类损失: {avg_classification_loss:.4f}")
    logger.info(f"  对比损失: {avg_contrastive_loss:.4f}")

def contrastive_enhanced_training(model, train_loader, val_loader, device, epochs, lr):
    """对比学习增强训练"""
    
    # 优化器
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    optimizer = optim.AdamW(trainable_params, lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)
    
    # 损失函数
    contrastive_criterion = ContrastiveLoss(temperature=0.1)
    classification_criterion = nn.CrossEntropyLoss(reduction='none')
    
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    for epoch in range(epochs):
        # 动态调整对比学习权重
        contrastive_weight = 0.8 * (1 - epoch / epochs) + 0.2  # 从0.8衰减到0.2
        
        # 训练
        contrastive_train_epoch(
            model, train_loader, optimizer, device, epoch, epochs,
            contrastive_criterion, classification_criterion,
            contrastive_weight=contrastive_weight, original_weight=2.5
        )
        
        scheduler.step()
        
        # 验证
        val_acc, original_acc, individual_acc = evaluate_contrastive_model(model, val_loader, device)
        
        logger.info(f"  验证结果 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
        logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        logger.info(f"  对比权重: {contrastive_weight:.3f}")
        
        # 早停和模型保存
        combined_score = 0.7 * original_acc + 0.3 * individual_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_dim': model.feature_dim,
                'num_classes': model.num_classes,
                'epoch': epoch,
                'val_acc': val_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc
            }, 'best_contrastive_model.pth')
            
            logger.info(f"  ✅ 新的最佳模型 (综合得分: {combined_score:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"早停触发 (连续{patience}轮无改进)")
                break
    
    logger.info(f"对比学习训练完成！最佳综合得分: {best_val_acc:.4f}")
    return model

def evaluate_contrastive_model(model, val_loader, device):
    """评估对比学习模型"""
    
    model.eval()
    
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        return 0.0, 0.0, 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # 使用更强的KNN分类器
    knn = KNeighborsClassifier(n_neighbors=min(9, len(set(all_labels))), 
                              metric='cosine', weights='distance')
    
    # 留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    # 计算各项准确率
    overall_acc = accuracy_score(all_labels, predictions)
    
    original_mask = all_is_original.astype(bool)
    original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
    
    individual_mask = ~original_mask
    individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
    
    return overall_acc, original_acc, individual_acc

def main():
    parser = argparse.ArgumentParser(description='对比学习增强训练')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='contrastive_enhanced_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=30, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=40, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据准备 (使用增强数据集)
    from enhanced_progressive_training import EnhancedProgressiveDataset
    
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    full_dataset = EnhancedProgressiveDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    # 数据分割
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * 0.85))
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"总类别数: {len(cat_to_id)}")
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    # 创建基础特征提取器
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从域适应模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建对比学习增强模型
    model = ContrastiveProgressiveModel(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 开始对比学习训练
    logger.info("开始对比学习增强训练...")
    model = contrastive_enhanced_training(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"对比学习增强模型已保存到: {args.output}")

if __name__ == "__main__":
    main()
