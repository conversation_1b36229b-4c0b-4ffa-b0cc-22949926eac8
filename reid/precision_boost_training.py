#!/usr/bin/env python3
"""
精度提升训练 - 在6GB GPU上追求99.5%准确率
使用内存友好但高效的优化策略

策略:
1. 保持T-224架构但优化特征提取
2. 极强的数据增强和TTA
3. 困难样本挖掘
4. 多模型集成
5. 伪标签学习
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.optim.lr_scheduler import CosineAnnealingWarmRestarts
from torchvision import transforms
from PIL import Image

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from few_shot_meta_learning import PrototypicalNetwork, FewShotDataset, meta_train_epoch, meta_evaluate
from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedFeatureExtractor(FeatureExtractorModel):
    """增强的特征提取器"""
    
    def __init__(self, feature_dim: int = 2048):
        super().__init__(feature_dim)
        
        # 添加特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.LayerNorm(feature_dim * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.LayerNorm(feature_dim),
            nn.GELU(),
            nn.Dropout(0.05)
        )
        
        # 多头注意力
        self.self_attention = nn.MultiheadAttention(feature_dim, num_heads=8, dropout=0.1)
        
    def forward(self, x):
        # 基础特征
        features = super().forward(x)
        
        # 特征增强
        enhanced_features = self.feature_enhancer(features)
        
        # 残差连接
        features = features + enhanced_features
        
        # 自注意力
        features = features.unsqueeze(0)  # (1, batch, feature_dim)
        attended_features, _ = self.self_attention(features, features, features)
        features = attended_features.squeeze(0)  # (batch, feature_dim)
        
        return features

class PrecisionDataset(FewShotDataset):
    """高精度数据集 - 强化数据增强"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 极强的训练时数据增强
        self.precision_transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomResizedCrop((224, 224), scale=(0.8, 1.0)),
            transforms.RandomHorizontalFlip(0.5),
            transforms.RandomRotation(25),
            transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.RandomPerspective(distortion_scale=0.15, p=0.3),
            transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.5)),
            transforms.RandomAdjustSharpness(sharpness_factor=1.5, p=0.3),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            transforms.RandomErasing(p=0.3, scale=(0.02, 0.15))
        ])
        
        # 测试时增强 (TTA)
        self.tta_transforms = [
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            transforms.Compose([
                transforms.Resize((240, 240)),
                transforms.CenterCrop((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]

class PrecisionBoostTrainer:
    """精度提升训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 训练状态
        self.best_accuracy = 0.0
        self.target_accuracy = 0.995  # 99.5%
        self.patience = 25
        self.patience_counter = 0
        
        # 困难样本存储
        self.hard_samples = []
        
    def find_hard_samples(self, model, dataset, threshold=0.8):
        """找到困难样本"""
        model.eval()
        hard_samples = []
        
        with torch.no_grad():
            for _ in range(50):  # 采样50个episodes
                support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                    n_way=min(8, len(dataset.classes)), k_shot=3, q_query=5
                )
                
                support_images = support_images.to(self.device)
                support_labels = support_labels.to(self.device)
                query_images = query_images.to(self.device)
                query_labels = query_labels.to(self.device)
                
                # 预测
                support_features = model(support_images)
                query_features = model(query_images)
                
                prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
                distances = torch.cdist(query_features, prototypes, p=2)
                similarities = -distances
                
                # 找到低置信度样本
                max_similarities = similarities.max(dim=1)[0]
                for i, (sim, label) in enumerate(zip(max_similarities, query_labels)):
                    if sim < threshold:  # 低置信度样本
                        hard_samples.append({
                            'image': query_images[i],
                            'label': label,
                            'confidence': sim.item()
                        })
        
        logger.info(f"发现 {len(hard_samples)} 个困难样本")
        return hard_samples
    
    def train_with_hard_mining(self, model, dataset, optimizer, device, epoch):
        """困难样本挖掘训练"""
        model.train()
        
        total_loss = 0.0
        total_accuracy = 0.0
        episodes_per_epoch = self.args.episodes_per_epoch
        
        # 每10轮更新困难样本
        if epoch % 10 == 0:
            self.hard_samples = self.find_hard_samples(model, dataset)
        
        for episode in range(episodes_per_epoch):
            # 正常episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(10, len(dataset.classes)), k_shot=3, q_query=8
            )
            
            # 添加困难样本 (30%概率)
            if self.hard_samples and random.random() < 0.3:
                num_hard = min(2, len(self.hard_samples))
                hard_indices = random.sample(range(len(self.hard_samples)), num_hard)
                
                for idx in hard_indices:
                    hard_sample = self.hard_samples[idx]
                    query_images = torch.cat([query_images, hard_sample['image'].unsqueeze(0)], dim=0)
                    query_labels = torch.cat([query_labels, hard_sample['label'].unsqueeze(0)], dim=0)
            
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)
            
            # 前向传播
            support_features = model(support_images)
            query_features = model(query_images)
            
            # 计算原型
            prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
            
            # 计算距离和损失
            distances = torch.cdist(query_features, prototypes, p=2)
            logits = -distances
            
            # 标签平滑
            loss = F.cross_entropy(logits, query_labels, label_smoothing=0.1)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            # 统计
            predictions = logits.argmax(dim=1)
            accuracy = (predictions == query_labels).float().mean()
            
            total_loss += loss.item()
            total_accuracy += accuracy.item()
        
        return total_loss / episodes_per_epoch, total_accuracy / episodes_per_epoch
    
    def test_time_augmentation(self, model, dataset, num_episodes=20):
        """测试时增强评估"""
        model.eval()
        
        total_accuracy = 0.0
        original_correct = 0
        individual_correct = 0
        original_total = 0
        individual_total = 0
        
        with torch.no_grad():
            for episode in range(num_episodes):
                support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                    n_way=min(10, len(dataset.classes)), k_shot=2, q_query=8
                )
                
                # TTA for support images
                support_features_list = []
                for transform in dataset.tta_transforms:
                    support_aug = torch.stack([transform(transforms.ToPILImage()(img)) 
                                             for img in support_images])
                    support_aug = support_aug.to(self.device)
                    support_features_list.append(model(support_aug))
                
                # 平均支持特征
                support_features = torch.stack(support_features_list).mean(dim=0)
                
                # TTA for query images
                query_features_list = []
                for transform in dataset.tta_transforms:
                    query_aug = torch.stack([transform(transforms.ToPILImage()(img)) 
                                           for img in query_images])
                    query_aug = query_aug.to(self.device)
                    query_features_list.append(model(query_aug))
                
                # 平均查询特征
                query_features = torch.stack(query_features_list).mean(dim=0)
                
                support_labels = support_labels.to(self.device)
                query_labels = query_labels.to(self.device)
                
                # 计算原型并分类
                prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
                predictions, _ = model.classify_by_prototypes(query_features, prototypes, prototype_labels)
                
                # 统计准确率
                accuracy = (predictions == query_labels).float().mean()
                total_accuracy += accuracy.item()
                
                # 分别统计原始3猫和个体猫
                for j, label in enumerate(query_labels):
                    if label.item() < 3:  # 原始3猫
                        original_total += 1
                        if predictions[j] == label:
                            original_correct += 1
                    else:  # 个体猫
                        individual_total += 1
                        if predictions[j] == label:
                            individual_correct += 1
        
        avg_accuracy = total_accuracy / num_episodes
        original_accuracy = original_correct / original_total if original_total > 0 else 0
        individual_accuracy = individual_correct / individual_total if individual_total > 0 else 0
        
        return avg_accuracy, original_accuracy, individual_accuracy
    
    def train(self):
        """执行精度提升训练"""
        logger.info("🚀 开始精度提升训练 - 目标99.5%准确率...")
        
        # 加载数据
        with open(self.args.annotations, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        # 创建cat_to_id映射
        original_cats = {'小白': 0, '小花': 1, '小黑': 2}
        cat_to_id = original_cats.copy()
        
        individual_dirs = [d for d in os.listdir(self.args.individual_data_dir) 
                          if os.path.isdir(os.path.join(self.args.individual_data_dir, d))]
        for i, cat_dir in enumerate(sorted(individual_dirs)):
            if cat_dir not in cat_to_id:
                cat_to_id[cat_dir] = len(cat_to_id)
        
        # 创建精度数据集
        dataset = PrecisionDataset(annotations, self.args.individual_data_dir, 
                                 self.args.original_dir, cat_to_id, 
                                 max_individual_cats=self.args.max_individual_cats)
        
        # 创建增强模型
        base_model = EnhancedFeatureExtractor(feature_dim=2048)
        model = PrototypicalNetwork(base_model, feature_dim=2048)
        
        # 加载预训练模型
        if self.args.pretrained and os.path.exists(self.args.pretrained):
            logger.info(f"加载预训练模型: {self.args.pretrained}")
            checkpoint = torch.load(self.args.pretrained, map_location=self.device, weights_only=False)
            # 尝试加载兼容的权重
            try:
                model.load_state_dict(checkpoint['model_state_dict'], strict=False)
                logger.info("✅ 预训练模型加载成功")
            except Exception as e:
                logger.warning(f"预训练模型加载失败: {e}")
        
        model.to(self.device)
        
        # 优化器和调度器
        optimizer = optim.AdamW(model.parameters(), lr=self.args.lr, 
                              weight_decay=1e-4, betas=(0.9, 0.999))
        scheduler = CosineAnnealingWarmRestarts(optimizer, T_0=15, T_mult=2, eta_min=1e-7)
        
        logger.info(f"开始训练 - 总轮数: {self.args.epochs}")
        
        # 训练循环
        for epoch in range(self.args.epochs):
            # 困难样本挖掘训练
            avg_loss, avg_accuracy = self.train_with_hard_mining(
                model, dataset, optimizer, self.device, epoch
            )
            
            scheduler.step()
            
            logger.info(f"Epoch {epoch + 1}: 损失={avg_loss:.4f}, 准确率={avg_accuracy:.4f}")
            
            # 每5轮进行TTA评估
            if (epoch + 1) % 5 == 0:
                val_acc, original_acc, individual_acc = self.test_time_augmentation(model, dataset)
                
                logger.info(f"  TTA验证 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
                
                if val_acc > self.best_accuracy:
                    self.best_accuracy = val_acc
                    self.patience_counter = 0
                    
                    # 保存最佳模型
                    torch.save({
                        'model_state_dict': model.state_dict(),
                        'feature_dim': 2048,
                        'cat_to_id': cat_to_id,
                        'epoch': epoch + 1,
                        'accuracy': val_acc,
                        'original_acc': original_acc,
                        'individual_acc': individual_acc
                    }, 'precision_boost_model.pth')
                    
                    logger.info(f"  ✅ 保存最佳模型 (准确率: {val_acc:.4f})")
                    
                    # 检查是否达到目标
                    if val_acc >= self.target_accuracy:
                        logger.info(f"🎉🎉🎉 达到目标准确率 {self.target_accuracy:.1%}!")
                        break
                else:
                    self.patience_counter += 1
                    if self.patience_counter >= self.patience:
                        logger.info(f"早停触发 (连续{self.patience}轮无改进)")
                        break
        
        logger.info(f"训练完成！最佳准确率: {self.best_accuracy:.4f} ({self.best_accuracy*100:.2f}%)")
        
        if self.best_accuracy >= self.target_accuracy:
            logger.info(f"🎉 成功达到目标准确率 {self.target_accuracy:.1%}!")
        else:
            logger.info(f"📈 距离目标还差 {(self.target_accuracy - self.best_accuracy)*100:.2f} 个百分点")

def main():
    parser = argparse.ArgumentParser(description='精度提升训练')
    parser.add_argument('--annotations', required=True, help='标注文件路径')
    parser.add_argument('--individual-data-dir', required=True, help='个体猫数据目录')
    parser.add_argument('--original-dir', required=True, help='原始猫数据目录')
    parser.add_argument('--pretrained', help='预训练模型路径')
    parser.add_argument('--max-individual-cats', type=int, default=50, help='最大个体猫数量')
    parser.add_argument('--epochs', type=int, default=150, help='训练轮数')
    parser.add_argument('--episodes-per-epoch', type=int, default=250, help='每轮episode数量')
    parser.add_argument('--lr', type=float, default=5e-6, help='学习率')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 创建训练器并开始训练
    trainer = PrecisionBoostTrainer(args)
    trainer.train()

if __name__ == "__main__":
    main()
