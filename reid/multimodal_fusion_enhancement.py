#!/usr/bin/env python3
"""
多模态融合增强 - 结合原始图像和红外图像特征提升识别准确率
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiModalDataset(Dataset):
    """多模态数据集 - 同时加载原始图像和红外图像"""
    
    def __init__(self, original_annotations: Dict, infrared_annotations: Dict,
                 individual_data_dir: str, original_dir: str, infrared_dir: str,
                 cat_to_id: Dict[str, int], max_individual_cats: int = 30, is_training=True):
        
        self.original_dir = original_dir
        self.infrared_dir = infrared_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.RandomRotation(10),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        self.samples = []
        
        # 处理原始3猫数据 - 同时包含原始图像和红外图像
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                # 查找对应的红外图像
                base_name = filename.replace('.jpg', '').replace('_hls', '')
                infrared_filename = f"{base_name}_hls_ir.jpg"
                
                if infrared_filename in infrared_annotations:
                    # 重复2次以保持性能
                    for _ in range(2):
                        self.samples.append({
                            'original_path': os.path.join(original_dir, filename),
                            'infrared_path': os.path.join(infrared_dir, infrared_filename),
                            'category': annotation['category'],
                            'label': cat_to_id[annotation['category']],
                            'is_original': True,
                            'has_infrared': True
                        })
        
        # 处理cat_individual_images数据 - 只有原始图像
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        # 重复2次增加训练机会
                        for _ in range(2):
                            self.samples.append({
                                'original_path': os.path.join(folder_path, img_file),
                                'infrared_path': None,  # 个体图像没有红外版本
                                'category': cat_name,
                                'label': cat_to_id[cat_name],
                                'is_original': False,
                                'has_infrared': False
                            })
        
        random.shuffle(self.samples)
        
        # 统计信息
        original_count = sum(1 for s in self.samples if s['is_original'])
        individual_count = len(self.samples) - original_count
        infrared_count = sum(1 for s in self.samples if s['has_infrared'])
        
        logger.info(f"多模态数据集: 原始3猫 {original_count} 样本, 个体猫咪 {individual_count} 样本")
        logger.info(f"红外图像: {infrared_count} 样本")
        logger.info(f"总类别数: {len(cat_to_id)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        # 加载原始图像
        try:
            original_image = Image.open(sample['original_path']).convert('RGB')
        except Exception as e:
            logger.error(f"无法加载原始图像 {sample['original_path']}: {e}")
            original_image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        # 加载红外图像（如果存在）
        if sample['has_infrared'] and sample['infrared_path']:
            try:
                infrared_image = Image.open(sample['infrared_path']).convert('RGB')
            except Exception as e:
                logger.error(f"无法加载红外图像 {sample['infrared_path']}: {e}")
                infrared_image = original_image.copy()  # 使用原始图像作为备用
        else:
            infrared_image = original_image.copy()  # 对于没有红外图像的样本，复制原始图像
        
        # 应用变换
        original_tensor = self.transform(original_image)
        infrared_tensor = self.transform(infrared_image)
        
        return (original_tensor, infrared_tensor, sample['label'], 
                sample['category'], sample['is_original'], sample['has_infrared'])

class MultiModalFusionModel(nn.Module):
    """多模态融合模型"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=33):
        super(MultiModalFusionModel, self).__init__()
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        # 原始图像特征提取器
        self.original_feature_extractor = base_model
        
        # 红外图像特征提取器（共享权重但有独立的增强层）
        self.infrared_feature_extractor = base_model  # 共享backbone
        
        # 冻结backbone
        for param in self.original_feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 原始图像特征增强
        self.original_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 红外图像特征增强
        self.infrared_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 跨模态注意力机制
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=feature_dim, num_heads=8, dropout=0.1, batch_first=True
        )
        
        # 模态融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(feature_dim * 2, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.4),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, original_images, infrared_images, has_infrared_mask, return_features=False):
        batch_size = original_images.shape[0]
        
        # 提取原始图像特征
        original_features = self.original_feature_extractor(original_images)
        original_enhanced = self.original_enhancer(original_features)
        
        # 提取红外图像特征
        infrared_features = self.infrared_feature_extractor(infrared_images)
        infrared_enhanced = self.infrared_enhancer(infrared_features)
        
        # 跨模态注意力（只对有红外图像的样本应用）
        if has_infrared_mask.sum() > 0:
            # 准备注意力输入
            original_for_attention = original_enhanced[has_infrared_mask].unsqueeze(1)  # [N, 1, D]
            infrared_for_attention = infrared_enhanced[has_infrared_mask].unsqueeze(1)  # [N, 1, D]
            
            # 应用跨模态注意力
            attended_original, _ = self.cross_attention(
                original_for_attention, infrared_for_attention, infrared_for_attention
            )
            attended_infrared, _ = self.cross_attention(
                infrared_for_attention, original_for_attention, original_for_attention
            )
            
            # 更新有红外图像的样本特征
            original_enhanced[has_infrared_mask] = attended_original.squeeze(1)
            infrared_enhanced[has_infrared_mask] = attended_infrared.squeeze(1)
        
        # 特征融合
        fused_features = torch.cat([original_enhanced, infrared_enhanced], dim=1)
        final_features = self.fusion_layer(fused_features)
        
        if return_features:
            return final_features
        
        # 分类
        logits = self.classifier(final_features)
        return logits, final_features

def multimodal_train_epoch(model, train_loader, optimizer, device, epoch, total_epochs):
    """多模态训练一个epoch"""
    
    model.train()
    criterion = nn.CrossEntropyLoss(reduction='none')
    
    total_loss = 0.0
    num_batches = 0
    
    for batch in train_loader:
        original_images, infrared_images, labels, categories, is_original, has_infrared = batch
        
        original_images = original_images.to(device)
        infrared_images = infrared_images.to(device)
        labels = labels.to(device)
        is_original = is_original.to(device)
        has_infrared = has_infrared.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        logits, features = model(original_images, infrared_images, has_infrared)
        
        # 计算损失
        losses = criterion(logits, labels)
        
        # 对原始3猫数据给予更高权重
        weights = torch.where(is_original, 2.5, 1.0)
        
        # 对有红外图像的样本给予额外奖励
        infrared_bonus = torch.where(has_infrared, 1.2, 1.0)
        weights = weights * infrared_bonus
        
        weighted_loss = (losses * weights).mean()
        
        # 特征正则化
        feature_reg = 0.001 * torch.norm(features, p=2, dim=1).mean()
        total_loss_batch = weighted_loss + feature_reg
        
        total_loss_batch.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += weighted_loss.item()
        num_batches += 1
    
    avg_loss = total_loss / num_batches
    logger.info(f"Epoch {epoch+1}/{total_epochs} - 训练损失: {avg_loss:.4f}")

def evaluate_multimodal_model(model, val_loader, device):
    """评估多模态模型"""
    
    model.eval()
    
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for batch in val_loader:
            original_images, infrared_images, labels, categories, is_original, has_infrared = batch
            
            original_images = original_images.to(device)
            infrared_images = infrared_images.to(device)
            has_infrared = has_infrared.to(device)
            
            features = model(original_images, infrared_images, has_infrared, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        return 0.0, 0.0, 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(9, len(set(all_labels))), 
                              metric='cosine', weights='distance')
    
    # 留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    # 计算准确率
    overall_acc = accuracy_score(all_labels, predictions)
    
    original_mask = all_is_original.astype(bool)
    original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
    
    individual_mask = ~original_mask
    individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
    
    return overall_acc, original_acc, individual_acc

def main():
    parser = argparse.ArgumentParser(description='多模态融合增强训练')
    parser.add_argument('--original-annotations', required=True, help='原始图像标注文件')
    parser.add_argument('--infrared-annotations', required=True, help='红外图像标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--infrared-dir', required=True, help='红外图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='multimodal_fusion_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=25, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=35, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=12, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载标注数据
    with open(args.original_annotations, 'r', encoding='utf-8') as f:
        original_annotations = json.load(f)
    
    with open(args.infrared_annotations, 'r', encoding='utf-8') as f:
        infrared_annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建多模态数据集
    full_dataset = MultiModalDataset(
        original_annotations, infrared_annotations, args.individual_data_dir,
        args.original_dir, args.infrared_dir, cat_to_id, args.max_individual_cats, True
    )
    
    # 数据分割
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * 0.85))
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    # 创建基础特征提取器
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从域适应模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建多模态融合模型
    model = MultiModalFusionModel(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 优化器和调度器
    trainable_param_list = [p for p in model.parameters() if p.requires_grad]
    optimizer = optim.AdamW(trainable_param_list, lr=args.lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=12, T_mult=2)
    
    # 训练循环
    best_val_acc = 0.0
    patience = 12
    patience_counter = 0
    
    logger.info("开始多模态融合训练...")
    
    for epoch in range(args.epochs):
        # 训练
        multimodal_train_epoch(model, train_loader, optimizer, device, epoch, args.epochs)
        scheduler.step()
        
        # 验证
        val_acc, original_acc, individual_acc = evaluate_multimodal_model(model, val_loader, device)
        
        logger.info(f"  验证结果 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
        logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停和模型保存
        combined_score = 0.7 * original_acc + 0.3 * individual_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_dim': model.feature_dim,
                'num_classes': model.num_classes,
                'epoch': epoch,
                'val_acc': val_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc
            }, 'best_multimodal_fusion_model.pth')
            
            logger.info(f"  ✅ 新的最佳模型 (综合得分: {combined_score:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"早停触发 (连续{patience}轮无改进)")
                break
    
    logger.info(f"多模态融合训练完成！最佳综合得分: {best_val_acc:.4f}")
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"多模态融合模型已保存到: {args.output}")

if __name__ == "__main__":
    main()
