[{"model_name": "对比学习模型", "overall_acc": 0.95, "original_acc": 1.0, "individual_acc": 0.6875, "inference_time": 0.010494425892829895, "throughput": 1488.885638868103, "grade": "🏆 优秀"}, {"model_name": "增强渐进式模型", "overall_acc": 0.882, "original_acc": 1.0, "individual_acc": 0.2625, "inference_time": 0.00959225744009018, "throughput": 1628.9179160993313, "grade": "❌ 需改进"}, {"model_name": "大规模训练模型", "overall_acc": 0.882, "original_acc": 1.0, "individual_acc": 0.2625, "inference_time": 0.010286979377269745, "throughput": 1518.9104038183668, "grade": "❌ 需改进"}, {"model_name": "基础渐进式模型", "overall_acc": 0.88, "original_acc": 1.0, "individual_acc": 0.25, "inference_time": 0.019390791654586792, "throughput": 805.7948472827816, "grade": "❌ 需改进"}]