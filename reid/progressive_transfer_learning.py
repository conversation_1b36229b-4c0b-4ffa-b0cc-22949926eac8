#!/usr/bin/env python3
"""
渐进式迁移学习 - 更简单有效的泛化能力提升方案
从已有的高性能模型开始，逐步扩展到新的猫咪个体
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProgressiveDataset(Dataset):
    """渐进式数据集 - 结合原有3猫数据和新的个体数据"""
    
    def __init__(self, original_annotations: Dict, individual_data_dir: str, 
                 original_dir: str, cat_to_id: Dict[str, int], 
                 max_individual_cats: int = 20, is_training=True):
        
        self.original_dir = original_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        self.samples = []
        
        # 处理原始3猫数据 - 给予更高权重
        original_weight = 3  # 原始数据重复3次以保持性能
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                for _ in range(original_weight):
                    self.samples.append({
                        'image_path': os.path.join(original_dir, filename),
                        'category': annotation['category'],
                        'label': cat_to_id[annotation['category']],
                        'is_original': True
                    })
        
        # 处理cat_individual_images数据
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            # 限制个体猫咪数量
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                # 为新个体分配ID
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        self.samples.append({
                            'image_path': os.path.join(folder_path, img_file),
                            'category': cat_name,
                            'label': cat_to_id[cat_name],
                            'is_original': False
                        })
        
        # 随机打乱样本
        random.shuffle(self.samples)
        
        # 统计信息
        original_count = sum(1 for s in self.samples if s['is_original'])
        individual_count = len(self.samples) - original_count
        logger.info(f"渐进式数据集: 原始3猫 {original_count} 样本, 个体猫咪 {individual_count} 样本")
        logger.info(f"总类别数: {len(cat_to_id)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        try:
            image = Image.open(sample['image_path']).convert('RGB')
        except Exception as e:
            logger.error(f"无法加载图像 {sample['image_path']}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        image = self.transform(image)
        return image, sample['label'], sample['category'], sample['is_original']

class ProgressiveModel(nn.Module):
    """渐进式模型 - 在原有模型基础上扩展"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=23):
        super(ProgressiveModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        # 冻结基础特征提取器的部分层
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 只训练特征增强层和分类器
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 扩展的分类器
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False):
        # 特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.feature_enhancer(base_features)
        
        if return_features:
            return enhanced_features
        
        # 分类
        logits = self.classifier(enhanced_features)
        return logits, enhanced_features

def train_progressive_model(model, train_loader, val_loader, device, epochs, lr):
    """训练渐进式模型"""
    
    # 优化器 - 只优化未冻结的参数
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    optimizer = optim.AdamW(trainable_params, lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)
    
    # 损失函数 - 对原始3猫使用更高权重
    criterion = nn.CrossEntropyLoss(reduction='none')
    
    best_val_acc = 0.0
    patience = 8
    patience_counter = 0
    
    for epoch in range(epochs):
        model.train()
        
        total_loss = 0.0
        num_batches = 0
        
        for images, labels, categories, is_original in train_loader:
            images = images.to(device)
            labels = labels.to(device)
            is_original = is_original.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            logits, features = model(images)
            
            # 计算损失
            losses = criterion(logits, labels)
            
            # 对原始3猫数据给予更高权重
            weights = torch.where(is_original, 2.0, 1.0)  # 原始数据权重x2
            weighted_loss = (losses * weights).mean()
            
            weighted_loss.backward()
            torch.nn.utils.clip_grad_norm_(trainable_params, max_norm=1.0)
            optimizer.step()
            
            total_loss += weighted_loss.item()
            num_batches += 1
        
        scheduler.step()
        
        # 验证
        val_acc, original_acc, individual_acc = evaluate_progressive_model(model, val_loader, device)
        
        avg_loss = total_loss / num_batches
        
        logger.info(f"Epoch {epoch+1}/{epochs}")
        logger.info(f"  训练损失: {avg_loss:.4f}")
        logger.info(f"  总体准确率: {val_acc:.4f}")
        logger.info(f"  原始3猫准确率: {original_acc:.4f}")
        logger.info(f"  个体猫咪准确率: {individual_acc:.4f}")
        logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        
        # 早停机制 - 重点关注原始3猫的性能
        combined_score = 0.7 * original_acc + 0.3 * val_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_dim': model.feature_dim,
                'num_classes': model.num_classes,
                'epoch': epoch,
                'val_acc': val_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc
            }, 'best_progressive_model.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"早停：性能连续{patience}轮未提升")
                break
    
    logger.info(f"训练完成！最佳综合得分: {best_val_acc:.4f}")
    return model

def evaluate_progressive_model(model, val_loader, device):
    """评估渐进式模型"""
    
    model.eval()
    
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        return 0.0, 0.0, 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # 使用KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(set(all_labels))), metric='cosine')
    
    # 简单的留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    # 计算总体准确率
    overall_acc = accuracy_score(all_labels, predictions)
    
    # 计算原始3猫准确率
    original_mask = all_is_original.astype(bool)
    if original_mask.sum() > 0:
        original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask])
    else:
        original_acc = 0.0
    
    # 计算个体猫咪准确率
    individual_mask = ~original_mask
    if individual_mask.sum() > 0:
        individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask])
    else:
        individual_acc = 0.0
    
    return overall_acc, original_acc, individual_acc

def main():
    parser = argparse.ArgumentParser(description='渐进式迁移学习')
    parser.add_argument('--annotations', required=True, help='原始图像标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='cat_individual_images数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型路径')
    parser.add_argument('--output', default='progressive_model.pth', help='输出模型路径')
    parser.add_argument('--max-individual-cats', type=int, default=20, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载标注数据
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 创建类别映射
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建数据集
    full_dataset = ProgressiveDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    # 分割训练和验证数据
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * 0.8))
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"总类别数: {len(cat_to_id)}")
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # 加载预训练模型
    logger.info("加载预训练的特征提取器...")
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)

    # 检查是否是域适应模型
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        # 这是域适应模型，需要提取基础特征提取器
        logger.info("从域适应模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)

        # 提取基础特征提取器的权重
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value

        base_model.load_state_dict(base_state_dict)
    else:
        # 这是基础特征提取器模型
        logger.info("加载基础特征提取器模型...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建渐进式模型
    model = ProgressiveModel(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 开始训练
    logger.info("开始渐进式迁移学习...")
    model = train_progressive_model(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"模型已保存到: {args.output}")
    logger.info("渐进式迁移学习完成！")

if __name__ == "__main__":
    main()
