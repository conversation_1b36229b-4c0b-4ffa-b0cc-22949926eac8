#!/usr/bin/env python3
"""
评估域对抗训练模型在cat_individual_images数据集上的泛化能力
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np
from collections import defaultdict

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from PIL import Image
import torchvision.transforms as transforms
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "infrared_cat_recognition_project" / "training"))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel

class CatIndividualDataset(Dataset):
    """cat_individual_images数据集加载器"""
    
    def __init__(self, data_dir: str, max_cats: int = None, images_per_cat: int = None):
        self.data_dir = data_dir
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 扫描所有猫咪文件夹
        self.cat_folders = []
        for folder in sorted(os.listdir(data_dir)):
            folder_path = os.path.join(data_dir, folder)
            if os.path.isdir(folder_path) and folder.isdigit():
                self.cat_folders.append(folder)
        
        # 限制猫咪数量
        if max_cats:
            self.cat_folders = self.cat_folders[:max_cats]
        
        # 构建样本列表
        self.samples = []
        self.cat_to_id = {}
        
        for cat_id, cat_folder in enumerate(self.cat_folders):
            self.cat_to_id[cat_folder] = cat_id
            folder_path = os.path.join(data_dir, cat_folder)
            
            # 获取该猫咪的所有图像
            images = []
            for img_file in os.listdir(folder_path):
                if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                    images.append(os.path.join(folder_path, img_file))
            
            # 限制每只猫的图像数量
            if images_per_cat and len(images) > images_per_cat:
                images = random.sample(images, images_per_cat)
            
            for img_path in images:
                self.samples.append({
                    'image_path': img_path,
                    'cat_id': cat_id,
                    'cat_name': cat_folder
                })
        
        print(f"加载了 {len(self.cat_folders)} 只猫咪，共 {len(self.samples)} 张图像")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        try:
            image = Image.open(sample['image_path']).convert('RGB')
            image = self.transform(image)
        except Exception as e:
            print(f"无法加载图像 {sample['image_path']}: {e}")
            image = torch.zeros(3, 224, 224)
        
        return image, sample['cat_id'], sample['cat_name']

def load_domain_adapted_model(model_path: str, device: torch.device):
    """加载域适应模型"""
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # 获取模型参数
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 3)
    
    # 创建基础特征提取器
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    
    # 创建域适应模型
    model = DomainAdaptationModel(base_model, feature_dim, num_classes)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    return model

def extract_features(model, dataloader, device):
    """提取特征"""
    
    features = []
    labels = []
    cat_names = []
    
    with torch.no_grad():
        for images, cat_ids, names in dataloader:
            images = images.to(device)
            
            # 提取特征
            batch_features = model(images, return_features=True)
            
            features.append(batch_features.cpu().numpy())
            labels.extend(cat_ids.numpy())
            cat_names.extend(names)
    
    features = np.vstack(features)
    return features, labels, cat_names

def evaluate_generalization(model_path: str, data_dir: str, max_cats: int = 50, 
                          test_ratio: float = 0.3):
    """评估泛化能力"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    print("加载域适应模型...")
    model = load_domain_adapted_model(model_path, device)
    
    # 创建数据集
    print(f"创建数据集 (最多 {max_cats} 只猫咪)...")
    dataset = CatIndividualDataset(data_dir, max_cats=max_cats, images_per_cat=20)
    
    if len(dataset.cat_folders) == 0:
        print("未找到有效的猫咪数据")
        return
    
    # 分割训练和测试数据
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(dataset.samples):
        samples_by_cat[sample['cat_id']].append(i)
    
    train_indices = []
    test_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = int(len(indices) * (1 - test_ratio))
        train_indices.extend(indices[:split_idx])
        test_indices.extend(indices[split_idx:])
    
    # 创建数据加载器
    train_dataset = torch.utils.data.Subset(dataset, train_indices)
    test_dataset = torch.utils.data.Subset(dataset, test_indices)
    
    train_loader = DataLoader(train_dataset, batch_size=16, shuffle=False, num_workers=2)
    test_loader = DataLoader(test_dataset, batch_size=16, shuffle=False, num_workers=2)
    
    print(f"训练集: {len(train_dataset)} 样本")
    print(f"测试集: {len(test_dataset)} 样本")
    
    # 提取特征
    print("提取训练集特征...")
    train_features, train_labels, train_names = extract_features(model, train_loader, device)
    
    print("提取测试集特征...")
    test_features, test_labels, test_names = extract_features(model, test_loader, device)
    
    # 特征标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    test_features_scaled = scaler.transform(test_features)
    
    # 使用KNN分类器
    print("训练KNN分类器...")
    knn = KNeighborsClassifier(n_neighbors=min(7, len(set(train_labels))), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    # 预测
    predictions = knn.predict(test_features_scaled)
    accuracy = accuracy_score(test_labels, predictions)
    
    print(f"\n=== 泛化能力评估结果 ===")
    print(f"测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
    print(f"猫咪数量: {len(dataset.cat_folders)}")
    print(f"训练样本: {len(train_dataset)}")
    print(f"测试样本: {len(test_dataset)}")
    
    # 详细分类报告
    unique_labels = sorted(set(test_labels))
    if len(unique_labels) <= 20:  # 只有类别数较少时才显示详细报告
        target_names = [f"Cat_{dataset.cat_folders[i]}" for i in unique_labels]
        report = classification_report(test_labels, predictions, 
                                     target_names=target_names, 
                                     labels=unique_labels)
        print(f"\n详细分类报告:\n{report}")
    
    # 分析结果
    print(f"\n=== 分析 ===")
    if accuracy >= 0.95:
        print("✅ 优秀！模型在新数据上表现出色，具有很强的泛化能力")
    elif accuracy >= 0.80:
        print("🔶 良好！模型有一定泛化能力，但还有提升空间")
    elif accuracy >= 0.60:
        print("⚠️  一般！模型泛化能力有限，需要进一步优化")
    else:
        print("❌ 较差！模型泛化能力不足，建议重新设计训练策略")
    
    return {
        'accuracy': accuracy,
        'num_cats': len(dataset.cat_folders),
        'train_samples': len(train_dataset),
        'test_samples': len(test_dataset),
        'predictions': predictions,
        'true_labels': test_labels
    }

def main():
    parser = argparse.ArgumentParser(description='评估域对抗训练模型的泛化能力')
    parser.add_argument('--model', required=True, help='域适应模型路径')
    parser.add_argument('--data-dir', required=True, help='cat_individual_images数据目录')
    parser.add_argument('--max-cats', type=int, default=50, help='最大猫咪数量')
    parser.add_argument('--test-ratio', type=float, default=0.3, help='测试集比例')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    if not os.path.exists(args.data_dir):
        print(f"数据目录不存在: {args.data_dir}")
        return
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 评估泛化能力
    results = evaluate_generalization(args.model, args.data_dir, args.max_cats, args.test_ratio)
    
    # 保存结果
    output_file = "generalization_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'accuracy': results['accuracy'],
            'num_cats': results['num_cats'],
            'train_samples': results['train_samples'],
            'test_samples': results['test_samples'],
            'model_path': args.model,
            'data_dir': args.data_dir,
            'max_cats': args.max_cats,
            'test_ratio': args.test_ratio
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
