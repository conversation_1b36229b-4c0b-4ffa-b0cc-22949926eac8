#!/usr/bin/env python3
"""
压缩模型测试脚本
测试压缩后模型的性能，包括准确率、速度和模型大小对比
"""

import os
import sys
import json
import argparse
import logging
import time
import random
from pathlib import Path
from typing import Dict, List, Tuple
import statistics

import torch
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score, classification_report
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel
from compress_domain_adapted_model import CompressedDomainAdaptationModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompressedModelTester:
    """压缩模型测试器"""
    
    def __init__(self, compressed_model_path: str, teacher_model_path: str = None, device: str = "auto"):
        self.compressed_model_path = compressed_model_path
        self.teacher_model_path = teacher_model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        
        self.compressed_model = None
        self.teacher_model = None
        self.cat_to_id = None
        self.id_to_cat = None
        self.scaler = StandardScaler()
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_models()
    
    def load_models(self):
        """加载压缩模型和教师模型"""
        logger.info(f"加载压缩模型: {self.compressed_model_path}")
        
        # 加载压缩模型
        checkpoint = torch.load(self.compressed_model_path, map_location=self.device, weights_only=False)
        
        feature_dim = checkpoint.get('feature_dim', 2048)
        compressed_dim = checkpoint.get('compressed_dim', 512)
        self.cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        self.id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
        
        # 创建基础模型
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['base_model_state_dict'])
        
        # 创建压缩模型
        self.compressed_model = CompressedDomainAdaptationModel(
            base_model, feature_dim, compressed_dim, len(self.cat_to_id)
        )
        self.compressed_model.load_state_dict(checkpoint['model_state_dict'])
        self.compressed_model = self.compressed_model.to(self.device)
        self.compressed_model.eval()
        
        logger.info(f"压缩模型加载完成，特征维度: {feature_dim} -> {compressed_dim}")
        
        # 加载教师模型（如果提供）
        if self.teacher_model_path:
            logger.info(f"加载教师模型: {self.teacher_model_path}")
            teacher_checkpoint = torch.load(self.teacher_model_path, map_location=self.device, weights_only=False)
            
            teacher_base_model = FeatureExtractorModel(feature_dim=feature_dim)
            teacher_base_model.load_state_dict(teacher_checkpoint['base_model_state_dict'])
            
            self.teacher_model = DomainAdaptationModel(teacher_base_model, feature_dim, len(self.cat_to_id))
            self.teacher_model.load_state_dict(teacher_checkpoint['model_state_dict'])
            self.teacher_model = self.teacher_model.to(self.device)
            self.teacher_model.eval()
            
            logger.info("教师模型加载完成")
    
    def extract_features(self, image_path: str, use_compressed=True) -> np.ndarray:
        """提取单张图像的特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                if use_compressed:
                    features = self.compressed_model(image_tensor, return_features=True)
                else:
                    features = self.teacher_model(image_tensor, return_features=True)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"提取特征失败 {image_path}: {e}")
            return np.zeros(512 if use_compressed else 2048)
    
    def build_reference_database(self, annotations_path: str, original_dir: str, use_compressed=True) -> Tuple[np.ndarray, np.ndarray]:
        """构建参考数据库"""
        logger.info(f"构建参考数据库（{'压缩' if use_compressed else '教师'}模型）...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 提取原始图像特征
        features_list = []
        labels_list = []
        
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                image_path = os.path.join(original_dir, filename)
                if os.path.exists(image_path):
                    features = self.extract_features(image_path, use_compressed)
                    features_list.append(features)
                    labels_list.append(self.cat_to_id[annotation['category']])
        
        features = np.vstack(features_list)
        labels = np.array(labels_list)
        
        # 特征标准化
        features_scaled = self.scaler.fit_transform(features)
        
        logger.info(f"参考数据库构建完成: {len(features)} 个样本")
        return features_scaled, labels
    
    def predict_single(self, image_path: str, ref_features: np.ndarray, ref_labels: np.ndarray, use_compressed=True) -> Tuple[str, float]:
        """预测单张图像"""
        # 提取特征
        features = self.extract_features(image_path, use_compressed)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # 使用KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(7, len(ref_features)), metric='cosine')
        knn.fit(ref_features, ref_labels)
        
        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)
        
        predicted_cat = self.id_to_cat[pred_label]
        
        return predicted_cat, confidence
    
    def accuracy_comparison_test(self, annotations_path: str, original_dir: str, infrared_dir: str, num_samples: int = 200) -> Dict:
        """准确率对比测试"""
        logger.info(f"开始准确率对比测试 ({num_samples} 个样本)...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 获取红外图像测试样本
        infrared_files = []
        for original_filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                base_name = original_filename.replace('.jpg', '')
                infrared_filename = f"{base_name}_ir.jpg"
                infrared_path = os.path.join(infrared_dir, infrared_filename)
                
                if os.path.exists(infrared_path):
                    infrared_files.append((infrared_filename, annotation['category']))
        
        # 随机采样
        test_samples = random.sample(infrared_files, min(num_samples, len(infrared_files)))
        
        results = {}
        
        # 测试压缩模型
        ref_features_compressed, ref_labels = self.build_reference_database(annotations_path, original_dir, True)
        
        compressed_predictions = []
        ground_truths = []
        compressed_confidences = []
        
        for filename, true_category in test_samples:
            image_path = os.path.join(infrared_dir, filename)
            pred_category, confidence = self.predict_single(image_path, ref_features_compressed, ref_labels, True)
            
            compressed_predictions.append(pred_category)
            ground_truths.append(true_category)
            compressed_confidences.append(confidence)
        
        compressed_accuracy = accuracy_score(ground_truths, compressed_predictions)
        results['compressed'] = {
            'accuracy': compressed_accuracy,
            'avg_confidence': statistics.mean(compressed_confidences),
            'confidence_std': statistics.stdev(compressed_confidences) if len(compressed_confidences) > 1 else 0
        }
        
        # 测试教师模型（如果可用）
        if self.teacher_model:
            ref_features_teacher, _ = self.build_reference_database(annotations_path, original_dir, False)
            
            teacher_predictions = []
            teacher_confidences = []
            
            for filename, true_category in test_samples:
                image_path = os.path.join(infrared_dir, filename)
                pred_category, confidence = self.predict_single(image_path, ref_features_teacher, ref_labels, False)
                
                teacher_predictions.append(pred_category)
                teacher_confidences.append(confidence)
            
            teacher_accuracy = accuracy_score(ground_truths, teacher_predictions)
            results['teacher'] = {
                'accuracy': teacher_accuracy,
                'avg_confidence': statistics.mean(teacher_confidences),
                'confidence_std': statistics.stdev(teacher_confidences) if len(teacher_confidences) > 1 else 0
            }
            
            results['accuracy_difference'] = compressed_accuracy - teacher_accuracy
        
        results['total_samples'] = len(test_samples)
        
        logger.info(f"准确率对比测试完成:")
        logger.info(f"  压缩模型准确率: {compressed_accuracy:.4f}")
        if self.teacher_model:
            logger.info(f"  教师模型准确率: {teacher_accuracy:.4f}")
            logger.info(f"  准确率差异: {results['accuracy_difference']:.4f}")
        
        return results
    
    def speed_comparison_test(self, infrared_dir: str, num_images: int = 100) -> Dict:
        """速度对比测试"""
        logger.info(f"开始速度对比测试 ({num_images} 张图像)...")
        
        # 获取测试图像
        image_files = [f for f in os.listdir(infrared_dir) if f.endswith('_ir.jpg')]
        test_images = random.sample(image_files, min(num_images, len(image_files)))
        
        results = {}
        
        # 测试压缩模型速度
        # 预热
        warmup_image = os.path.join(infrared_dir, test_images[0])
        for _ in range(5):
            self.extract_features(warmup_image, True)
        
        # 测试
        start_time = time.time()
        for image_file in test_images:
            image_path = os.path.join(infrared_dir, image_file)
            self.extract_features(image_path, True)
        end_time = time.time()
        
        compressed_time = end_time - start_time
        compressed_throughput = len(test_images) / compressed_time
        
        results['compressed'] = {
            'total_time': compressed_time,
            'throughput': compressed_throughput,
            'avg_time_per_image': compressed_time / len(test_images)
        }
        
        # 测试教师模型速度（如果可用）
        if self.teacher_model:
            # 预热
            for _ in range(5):
                self.extract_features(warmup_image, False)
            
            # 测试
            start_time = time.time()
            for image_file in test_images:
                image_path = os.path.join(infrared_dir, image_file)
                self.extract_features(image_path, False)
            end_time = time.time()
            
            teacher_time = end_time - start_time
            teacher_throughput = len(test_images) / teacher_time
            
            results['teacher'] = {
                'total_time': teacher_time,
                'throughput': teacher_throughput,
                'avg_time_per_image': teacher_time / len(test_images)
            }
            
            results['speedup'] = teacher_time / compressed_time
        
        results['total_images'] = len(test_images)
        
        logger.info(f"速度对比测试完成:")
        logger.info(f"  压缩模型吞吐量: {compressed_throughput:.2f} 图像/秒")
        if self.teacher_model:
            logger.info(f"  教师模型吞吐量: {teacher_throughput:.2f} 图像/秒")
            logger.info(f"  速度提升: {results['speedup']:.2f}x")
        
        return results
    
    def model_size_comparison(self) -> Dict:
        """模型大小对比"""
        logger.info("开始模型大小对比...")
        
        def calculate_model_size(model):
            param_size = 0
            buffer_size = 0
            
            for param in model.parameters():
                param_size += param.nelement() * param.element_size()
            
            for buffer in model.buffers():
                buffer_size += buffer.nelement() * buffer.element_size()
            
            size_mb = (param_size + buffer_size) / 1024 / 1024
            return size_mb
        
        compressed_size = calculate_model_size(self.compressed_model)
        
        results = {
            'compressed_size_mb': compressed_size
        }
        
        if self.teacher_model:
            teacher_size = calculate_model_size(self.teacher_model)
            results['teacher_size_mb'] = teacher_size
            results['compression_ratio'] = teacher_size / compressed_size
            results['size_reduction_mb'] = teacher_size - compressed_size
            results['size_reduction_percent'] = (teacher_size - compressed_size) / teacher_size * 100
        
        logger.info(f"模型大小对比完成:")
        logger.info(f"  压缩模型大小: {compressed_size:.2f} MB")
        if self.teacher_model:
            logger.info(f"  教师模型大小: {results['teacher_size_mb']:.2f} MB")
            logger.info(f"  压缩比: {results['compression_ratio']:.2f}x")
            logger.info(f"  大小减少: {results['size_reduction_mb']:.2f} MB ({results['size_reduction_percent']:.1f}%)")
        
        return results

def main():
    parser = argparse.ArgumentParser(description="压缩模型测试")
    parser.add_argument("--compressed-model", type=str, required=True, help="压缩模型路径")
    parser.add_argument("--teacher-model", type=str, help="教师模型路径（可选）")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--accuracy-samples", type=int, default=200, help="准确率测试样本数")
    parser.add_argument("--speed-samples", type=int, default=100, help="速度测试样本数")
    parser.add_argument("--output", type=str, default="compressed_model_test_results.json", help="结果输出文件")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 创建测试器
    tester = CompressedModelTester(args.compressed_model, args.teacher_model, args.device)
    
    # 执行测试
    results = {}
    
    # 准确率对比测试
    results['accuracy_comparison'] = tester.accuracy_comparison_test(
        args.annotations, args.original_dir, args.infrared_dir, args.accuracy_samples
    )
    
    # 速度对比测试
    results['speed_comparison'] = tester.speed_comparison_test(args.infrared_dir, args.speed_samples)
    
    # 模型大小对比
    results['size_comparison'] = tester.model_size_comparison()
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"测试完成! 结果已保存: {args.output}")
    
    # 打印总结
    print("\n" + "="*60)
    print("压缩模型测试总结")
    print("="*60)
    print(f"压缩模型准确率: {results['accuracy_comparison']['compressed']['accuracy']:.4f}")
    if 'teacher' in results['accuracy_comparison']:
        print(f"教师模型准确率: {results['accuracy_comparison']['teacher']['accuracy']:.4f}")
        print(f"准确率差异: {results['accuracy_comparison']['accuracy_difference']:.4f}")
    
    print(f"压缩模型吞吐量: {results['speed_comparison']['compressed']['throughput']:.2f} 图像/秒")
    if 'teacher' in results['speed_comparison']:
        print(f"教师模型吞吐量: {results['speed_comparison']['teacher']['throughput']:.2f} 图像/秒")
        print(f"速度提升: {results['speed_comparison']['speedup']:.2f}x")
    
    print(f"压缩模型大小: {results['size_comparison']['compressed_size_mb']:.2f} MB")
    if 'teacher_size_mb' in results['size_comparison']:
        print(f"教师模型大小: {results['size_comparison']['teacher_size_mb']:.2f} MB")
        print(f"压缩比: {results['size_comparison']['compression_ratio']:.2f}x")
    print("="*60)

if __name__ == "__main__":
    main()
