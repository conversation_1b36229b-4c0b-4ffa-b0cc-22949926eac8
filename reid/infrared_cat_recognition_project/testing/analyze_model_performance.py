#!/usr/bin/env python3
"""
模型性能分析脚本
详细分析原始模型和压缩模型的性能差异，找出吞吐量下降的原因
"""

import os
import sys
import json
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, List, Tuple
import statistics

import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
import torch.profiler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel
from compress_domain_adapted_model import CompressedDomainAdaptationModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelPerformanceAnalyzer:
    """模型性能分析器"""
    
    def __init__(self, teacher_model_path: str, compressed_model_path: str, device: str = "auto"):
        self.teacher_model_path = teacher_model_path
        self.compressed_model_path = compressed_model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        
        self.teacher_model = None
        self.compressed_model = None
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_models()
    
    def load_models(self):
        """加载教师模型和压缩模型"""
        logger.info("加载模型...")
        
        # 加载教师模型
        teacher_checkpoint = torch.load(self.teacher_model_path, map_location=self.device, weights_only=False)
        feature_dim = teacher_checkpoint.get('feature_dim', 2048)
        cat_to_id = teacher_checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        
        teacher_base_model = FeatureExtractorModel(feature_dim=feature_dim)
        teacher_base_model.load_state_dict(teacher_checkpoint['base_model_state_dict'])
        
        self.teacher_model = DomainAdaptationModel(teacher_base_model, feature_dim, len(cat_to_id))
        self.teacher_model.load_state_dict(teacher_checkpoint['model_state_dict'])
        self.teacher_model = self.teacher_model.to(self.device)
        self.teacher_model.eval()
        
        # 加载压缩模型
        compressed_checkpoint = torch.load(self.compressed_model_path, map_location=self.device, weights_only=False)
        compressed_dim = compressed_checkpoint.get('compressed_dim', 512)
        
        compressed_base_model = FeatureExtractorModel(feature_dim=feature_dim)
        compressed_base_model.load_state_dict(compressed_checkpoint['base_model_state_dict'])
        
        self.compressed_model = CompressedDomainAdaptationModel(
            compressed_base_model, feature_dim, compressed_dim, len(cat_to_id)
        )
        self.compressed_model.load_state_dict(compressed_checkpoint['model_state_dict'])
        self.compressed_model = self.compressed_model.to(self.device)
        self.compressed_model.eval()
        
        logger.info(f"教师模型加载完成: {feature_dim}维特征")
        logger.info(f"压缩模型加载完成: {feature_dim} -> {compressed_dim}维特征")
    
    def analyze_model_structure(self) -> Dict:
        """分析模型结构"""
        logger.info("分析模型结构...")
        
        def count_parameters(model):
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            return total_params, trainable_params
        
        def get_model_size(model):
            param_size = 0
            buffer_size = 0
            
            for param in model.parameters():
                param_size += param.nelement() * param.element_size()
            
            for buffer in model.buffers():
                buffer_size += buffer.nelement() * buffer.element_size()
            
            size_mb = (param_size + buffer_size) / 1024 / 1024
            return size_mb
        
        # 教师模型分析
        teacher_total, teacher_trainable = count_parameters(self.teacher_model)
        teacher_size = get_model_size(self.teacher_model)
        
        # 压缩模型分析
        compressed_total, compressed_trainable = count_parameters(self.compressed_model)
        compressed_size = get_model_size(self.compressed_model)
        
        # 分析各个组件
        teacher_components = {}
        compressed_components = {}
        
        # 教师模型组件
        teacher_components['feature_extractor'] = count_parameters(self.teacher_model.feature_extractor)[0]
        teacher_components['feature_enhancer'] = count_parameters(self.teacher_model.feature_enhancer)[0]
        teacher_components['cat_classifier'] = count_parameters(self.teacher_model.cat_classifier)[0]
        teacher_components['domain_classifier'] = count_parameters(self.teacher_model.domain_classifier)[0]
        
        # 压缩模型组件
        compressed_components['feature_extractor'] = count_parameters(self.compressed_model.feature_extractor)[0]
        compressed_components['feature_compressor'] = count_parameters(self.compressed_model.feature_compressor)[0]
        compressed_components['cat_classifier'] = count_parameters(self.compressed_model.cat_classifier)[0]
        
        return {
            'teacher': {
                'total_parameters': teacher_total,
                'trainable_parameters': teacher_trainable,
                'size_mb': teacher_size,
                'components': teacher_components
            },
            'compressed': {
                'total_parameters': compressed_total,
                'trainable_parameters': compressed_trainable,
                'size_mb': compressed_size,
                'components': compressed_components
            },
            'compression_ratio': teacher_total / compressed_total,
            'size_reduction': teacher_size - compressed_size
        }
    
    def benchmark_inference_speed(self, num_warmup: int = 10, num_runs: int = 100) -> Dict:
        """基准测试推理速度"""
        logger.info(f"基准测试推理速度 (预热: {num_warmup}, 测试: {num_runs})...")
        
        # 创建测试输入
        dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
        
        results = {}
        
        # 测试教师模型
        logger.info("测试教师模型...")
        
        # 预热
        with torch.no_grad():
            for _ in range(num_warmup):
                _ = self.teacher_model(dummy_input, return_features=True)
        
        # 同步GPU
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        # 测试
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = self.teacher_model(dummy_input, return_features=True)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                end_time = time.time()
                times.append(end_time - start_time)
        
        results['teacher'] = {
            'avg_time': statistics.mean(times),
            'std_time': statistics.stdev(times) if len(times) > 1 else 0,
            'min_time': min(times),
            'max_time': max(times),
            'throughput': 1.0 / statistics.mean(times)
        }
        
        # 测试压缩模型
        logger.info("测试压缩模型...")
        
        # 预热
        with torch.no_grad():
            for _ in range(num_warmup):
                _ = self.compressed_model(dummy_input, return_features=True)
        
        # 同步GPU
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        # 测试
        times = []
        with torch.no_grad():
            for _ in range(num_runs):
                start_time = time.time()
                _ = self.compressed_model(dummy_input, return_features=True)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                end_time = time.time()
                times.append(end_time - start_time)
        
        results['compressed'] = {
            'avg_time': statistics.mean(times),
            'std_time': statistics.stdev(times) if len(times) > 1 else 0,
            'min_time': min(times),
            'max_time': max(times),
            'throughput': 1.0 / statistics.mean(times)
        }
        
        # 计算性能比较
        results['comparison'] = {
            'speedup': results['teacher']['avg_time'] / results['compressed']['avg_time'],
            'throughput_ratio': results['compressed']['throughput'] / results['teacher']['throughput']
        }
        
        return results
    
    def profile_model_layers(self, num_runs: int = 10) -> Dict:
        """分析模型各层的性能"""
        logger.info("分析模型各层性能...")
        
        dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
        
        results = {}
        
        # 分析教师模型
        logger.info("分析教师模型各层...")
        
        def profile_teacher():
            with torch.no_grad():
                # 特征提取
                start_time = time.time()
                base_features = self.teacher_model.feature_extractor(dummy_input)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                feature_extraction_time = time.time() - start_time
                
                # 特征增强
                start_time = time.time()
                enhanced_features = self.teacher_model.feature_enhancer(base_features)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                feature_enhancement_time = time.time() - start_time
                
                # 分类
                start_time = time.time()
                cat_logits = self.teacher_model.cat_classifier(enhanced_features)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                classification_time = time.time() - start_time
                
                # 域分类
                start_time = time.time()
                domain_logits = self.teacher_model.domain_classifier(enhanced_features)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                domain_classification_time = time.time() - start_time
                
                return {
                    'feature_extraction': feature_extraction_time,
                    'feature_enhancement': feature_enhancement_time,
                    'classification': classification_time,
                    'domain_classification': domain_classification_time
                }
        
        # 运行多次取平均
        teacher_times = []
        for _ in range(num_runs):
            teacher_times.append(profile_teacher())
        
        # 计算平均时间
        results['teacher'] = {}
        for key in teacher_times[0].keys():
            times = [t[key] for t in teacher_times]
            results['teacher'][key] = {
                'avg_time': statistics.mean(times),
                'std_time': statistics.stdev(times) if len(times) > 1 else 0
            }
        
        # 分析压缩模型
        logger.info("分析压缩模型各层...")
        
        def profile_compressed():
            with torch.no_grad():
                # 特征提取
                start_time = time.time()
                base_features = self.compressed_model.feature_extractor(dummy_input)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                feature_extraction_time = time.time() - start_time
                
                # 特征压缩
                start_time = time.time()
                compressed_features = self.compressed_model.feature_compressor(base_features)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                feature_compression_time = time.time() - start_time
                
                # 分类
                start_time = time.time()
                cat_logits = self.compressed_model.cat_classifier(compressed_features)
                if self.device.type == 'cuda':
                    torch.cuda.synchronize()
                classification_time = time.time() - start_time
                
                return {
                    'feature_extraction': feature_extraction_time,
                    'feature_compression': feature_compression_time,
                    'classification': classification_time
                }
        
        # 运行多次取平均
        compressed_times = []
        for _ in range(num_runs):
            compressed_times.append(profile_compressed())
        
        # 计算平均时间
        results['compressed'] = {}
        for key in compressed_times[0].keys():
            times = [t[key] for t in compressed_times]
            results['compressed'][key] = {
                'avg_time': statistics.mean(times),
                'std_time': statistics.stdev(times) if len(times) > 1 else 0
            }
        
        return results
    
    def analyze_memory_usage(self) -> Dict:
        """分析内存使用情况"""
        logger.info("分析内存使用情况...")
        
        dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
        
        results = {}
        
        if self.device.type == 'cuda':
            # 清空GPU缓存
            torch.cuda.empty_cache()
            
            # 测试教师模型
            torch.cuda.reset_peak_memory_stats()
            with torch.no_grad():
                _ = self.teacher_model(dummy_input, return_features=True)
            
            teacher_memory = torch.cuda.max_memory_allocated() / 1024 / 1024  # MB
            
            # 清空GPU缓存
            torch.cuda.empty_cache()
            
            # 测试压缩模型
            torch.cuda.reset_peak_memory_stats()
            with torch.no_grad():
                _ = self.compressed_model(dummy_input, return_features=True)
            
            compressed_memory = torch.cuda.max_memory_allocated() / 1024 / 1024  # MB
            
            results = {
                'teacher_memory_mb': teacher_memory,
                'compressed_memory_mb': compressed_memory,
                'memory_reduction_mb': teacher_memory - compressed_memory,
                'memory_reduction_ratio': teacher_memory / compressed_memory
            }
        else:
            results = {
                'note': 'GPU内存分析需要CUDA设备'
            }
        
        return results

def main():
    parser = argparse.ArgumentParser(description="模型性能分析")
    parser.add_argument("--teacher-model", type=str, required=True, help="教师模型路径")
    parser.add_argument("--compressed-model", type=str, required=True, help="压缩模型路径")
    parser.add_argument("--output", type=str, default="model_performance_analysis.json", help="结果输出文件")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = ModelPerformanceAnalyzer(args.teacher_model, args.compressed_model, args.device)
    
    # 执行分析
    results = {}
    
    # 模型结构分析
    results['structure_analysis'] = analyzer.analyze_model_structure()
    
    # 推理速度基准测试
    results['speed_benchmark'] = analyzer.benchmark_inference_speed()
    
    # 各层性能分析
    results['layer_profiling'] = analyzer.profile_model_layers()
    
    # 内存使用分析
    results['memory_analysis'] = analyzer.analyze_memory_usage()
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"性能分析完成! 结果已保存: {args.output}")
    
    # 打印总结
    structure = results['structure_analysis']
    speed = results['speed_benchmark']
    
    print("\n" + "="*60)
    print("模型性能分析总结")
    print("="*60)
    
    print("模型结构对比:")
    print(f"  教师模型参数: {structure['teacher']['total_parameters']:,}")
    print(f"  压缩模型参数: {structure['compressed']['total_parameters']:,}")
    print(f"  参数压缩比: {structure['compression_ratio']:.2f}x")
    print(f"  大小减少: {structure['size_reduction']:.2f} MB")
    
    print("\n推理速度对比:")
    print(f"  教师模型吞吐量: {speed['teacher']['throughput']:.2f} 图像/秒")
    print(f"  压缩模型吞吐量: {speed['compressed']['throughput']:.2f} 图像/秒")
    print(f"  速度比: {speed['comparison']['speedup']:.2f}x")
    
    if speed['comparison']['speedup'] < 1.0:
        print(f"  ⚠️  压缩模型比教师模型慢 {1/speed['comparison']['speedup']:.2f}x")
    else:
        print(f"  ✅ 压缩模型比教师模型快 {speed['comparison']['speedup']:.2f}x")
    
    # 分析性能瓶颈
    layer_prof = results['layer_profiling']
    print("\n性能瓶颈分析:")
    
    teacher_total = sum(layer['avg_time'] for layer in layer_prof['teacher'].values())
    compressed_total = sum(layer['avg_time'] for layer in layer_prof['compressed'].values())
    
    print(f"  教师模型总时间: {teacher_total*1000:.2f} ms")
    print(f"  压缩模型总时间: {compressed_total*1000:.2f} ms")
    
    print("\n各层耗时对比:")
    for layer in layer_prof['teacher']:
        teacher_time = layer_prof['teacher'][layer]['avg_time'] * 1000
        print(f"  教师模型 {layer}: {teacher_time:.2f} ms")
    
    for layer in layer_prof['compressed']:
        compressed_time = layer_prof['compressed'][layer]['avg_time'] * 1000
        print(f"  压缩模型 {layer}: {compressed_time:.2f} ms")
    
    print("="*60)

if __name__ == "__main__":
    main()
