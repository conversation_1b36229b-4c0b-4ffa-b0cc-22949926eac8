#!/usr/bin/env python3
"""
域适应模型压缩脚本
对100%准确率的域适应模型进行压缩优化，保持性能的同时减小模型大小
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import time

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel, DualDomainDataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompressedDomainAdaptationModel(nn.Module):
    """压缩版域适应模型"""
    
    def __init__(self, base_model, feature_dim=2048, compressed_dim=512, num_classes=3):
        super(CompressedDomainAdaptationModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.compressed_dim = compressed_dim
        
        # 特征压缩层
        self.feature_compressor = nn.Sequential(
            nn.Linear(feature_dim, compressed_dim * 2),
            nn.BatchNorm1d(compressed_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(compressed_dim * 2, compressed_dim),
            nn.BatchNorm1d(compressed_dim),
            nn.ReLU()
        )
        
        # 压缩后的分类器
        self.cat_classifier = nn.Sequential(
            nn.Linear(compressed_dim, compressed_dim // 2),
            nn.BatchNorm1d(compressed_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(compressed_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False):
        # 特征提取
        base_features = self.feature_extractor(x)
        
        # 特征压缩
        compressed_features = self.feature_compressor(base_features)
        
        if return_features:
            return compressed_features
        
        # 分类
        cat_logits = self.cat_classifier(compressed_features)
        
        return cat_logits, compressed_features

def knowledge_distillation_loss(student_logits, teacher_logits, temperature=4.0, alpha=0.7):
    """知识蒸馏损失函数"""
    # 软标签损失
    soft_loss = nn.KLDivLoss(reduction='batchmean')(
        torch.log_softmax(student_logits / temperature, dim=1),
        torch.softmax(teacher_logits / temperature, dim=1)
    ) * (temperature ** 2)
    
    return soft_loss

def compress_domain_model(teacher_model, train_loader, val_loader, device, 
                         compressed_dim=512, num_epochs=30, lr=1e-4):
    """使用知识蒸馏压缩域适应模型"""
    
    logger.info(f"开始模型压缩，压缩维度: {compressed_dim}")
    
    # 创建学生模型（压缩版）
    base_model = teacher_model.feature_extractor
    student_model = CompressedDomainAdaptationModel(
        base_model, teacher_model.feature_dim, compressed_dim, 3
    ).to(device)
    
    # 冻结特征提取器（只训练压缩层和分类器）
    for param in student_model.feature_extractor.parameters():
        param.requires_grad = False
    
    # 损失函数和优化器
    ce_loss = nn.CrossEntropyLoss()
    optimizer = optim.AdamW(
        filter(lambda p: p.requires_grad, student_model.parameters()), 
        lr=lr, weight_decay=1e-4
    )
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-6)
    
    teacher_model.eval()  # 教师模型设为评估模式
    
    best_val_acc = 0.0
    patience = 10
    patience_counter = 0
    
    for epoch in range(num_epochs):
        student_model.train()
        total_loss = 0.0
        num_batches = 0
        
        for images, cat_labels, domain_labels, _ in train_loader:
            images = images.to(device)
            cat_labels = cat_labels.to(device)
            
            optimizer.zero_grad()
            
            # 教师模型预测（不计算梯度）
            with torch.no_grad():
                teacher_output = teacher_model(images)
                if isinstance(teacher_output, tuple) and len(teacher_output) >= 2:
                    teacher_logits = teacher_output[0]
                else:
                    teacher_logits = teacher_output
            
            # 学生模型预测
            student_logits, _ = student_model(images)
            
            # 计算损失
            # 硬标签损失
            hard_loss = ce_loss(student_logits, cat_labels)
            
            # 知识蒸馏损失
            distill_loss = knowledge_distillation_loss(student_logits, teacher_logits)
            
            # 总损失
            total_loss_batch = 0.3 * hard_loss + 0.7 * distill_loss
            
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += total_loss_batch.item()
            num_batches += 1
        
        scheduler.step()
        
        # 验证
        if (epoch + 1) % 3 == 0:
            val_acc = evaluate_compressed_model(student_model, train_loader, val_loader, device)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            logger.info(f"  训练损失: {total_loss/num_batches:.4f}")
            logger.info(f"  验证准确率: {val_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.2e}")
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                logger.info(f"  新的最佳验证准确率: {best_val_acc:.4f}")
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                logger.info("早停")
                break
    
    logger.info(f"模型压缩完成! 最佳验证准确率: {best_val_acc:.4f}")
    return student_model

def evaluate_compressed_model(model, train_loader, val_loader, device):
    """评估压缩模型"""
    
    model.eval()
    
    # 提取训练集特征（只使用原始图像作为参考库）
    train_features = []
    train_labels = []
    
    with torch.no_grad():
        for images, cat_labels, domain_labels, _ in train_loader:
            # 只使用原始图像域的样本作为参考
            original_mask = domain_labels == 0
            if original_mask.sum() > 0:
                original_images = images[original_mask].to(device)
                original_labels = cat_labels[original_mask]
                
                features = model(original_images, return_features=True)
                train_features.append(features.cpu().numpy())
                train_labels.append(original_labels.numpy())
    
    if len(train_features) == 0:
        return 0.0
    
    train_features = np.vstack(train_features)
    train_labels = np.hstack(train_labels)
    
    # 提取验证集特征（只评估红外图像）
    val_features = []
    val_labels = []
    
    with torch.no_grad():
        for images, cat_labels, domain_labels, _ in val_loader:
            # 只评估红外图像域的样本
            infrared_mask = domain_labels == 1
            if infrared_mask.sum() > 0:
                infrared_images = images[infrared_mask].to(device)
                infrared_labels = cat_labels[infrared_mask]
                
                features = model(infrared_images, return_features=True)
                val_features.append(features.cpu().numpy())
                val_labels.append(infrared_labels.numpy())
    
    if len(val_features) == 0:
        return 0.0
    
    val_features = np.vstack(val_features)
    val_labels = np.hstack(val_labels)
    
    # 特征标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    val_features_scaled = scaler.transform(val_features)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(train_features)), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    val_pred = knn.predict(val_features_scaled)
    accuracy = accuracy_score(val_labels, val_pred)
    
    return accuracy

def calculate_model_size(model):
    """计算模型大小"""
    param_size = 0
    buffer_size = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
    
    size_mb = (param_size + buffer_size) / 1024 / 1024
    return size_mb

def create_dual_domain_datasets_for_compression(annotations_path, original_dir, infrared_dir, train_ratio=0.8):
    """为压缩创建双域数据集"""
    
    # 加载标注
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外标注映射
    infrared_annotations = {}
    for original_filename, annotation in annotations_dict.items():
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        infrared_path = os.path.join(infrared_dir, infrared_filename)
        
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
    
    # 创建类别映射
    categories = list(set(ann['category'] for ann in annotations_dict.values()))
    categories = [cat for cat in categories if cat != "无"]
    categories.sort()
    
    cat_to_id = {cat: idx for idx, cat in enumerate(categories)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    # 按类别分割数据
    train_original = {}
    val_original = {}
    train_infrared = {}
    val_infrared = {}
    
    for cat in categories:
        # 原始图像
        cat_original = [(k, v) for k, v in annotations_dict.items() if v['category'] == cat]
        import random
        random.shuffle(cat_original)
        split_idx = max(1, int(len(cat_original) * train_ratio))
        
        for k, v in cat_original[:split_idx]:
            train_original[k] = v
        for k, v in cat_original[split_idx:]:
            val_original[k] = v
        
        # 红外图像
        cat_infrared = [(k, v) for k, v in infrared_annotations.items() if v['category'] == cat]
        random.shuffle(cat_infrared)
        split_idx = max(1, int(len(cat_infrared) * train_ratio))
        
        for k, v in cat_infrared[:split_idx]:
            train_infrared[k] = v
        for k, v in cat_infrared[split_idx:]:
            val_infrared[k] = v
    
    # 创建数据集
    train_dataset = DualDomainDataset(
        train_original, train_infrared, original_dir, infrared_dir, cat_to_id, True
    )
    val_dataset = DualDomainDataset(
        val_original, val_infrared, original_dir, infrared_dir, cat_to_id, False
    )
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def main():
    parser = argparse.ArgumentParser(description="域适应模型压缩")
    parser.add_argument("--teacher-model", type=str, required=True, help="教师模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output", type=str, default="./compressed_domain_model.pth", help="输出模型路径")
    parser.add_argument("--compressed-dim", type=int, default=512, help="压缩后特征维度")
    parser.add_argument("--epochs", type=int, default=30, help="训练轮数")
    parser.add_argument("--lr", type=float, default=1e-4, help="学习率")
    parser.add_argument("--batch-size", type=int, default=8, help="批次大小")
    
    args = parser.parse_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载教师模型
    logger.info(f"加载教师模型: {args.teacher_model}")
    checkpoint = torch.load(args.teacher_model, map_location=device, weights_only=False)
    
    feature_dim = checkpoint.get('feature_dim', 2048)
    cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
    id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
    
    # 创建教师模型
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    base_model.load_state_dict(checkpoint['base_model_state_dict'])
    
    teacher_model = DomainAdaptationModel(base_model, feature_dim, len(cat_to_id))
    teacher_model.load_state_dict(checkpoint['model_state_dict'])
    teacher_model = teacher_model.to(device)
    
    # 计算教师模型大小
    teacher_size = calculate_model_size(teacher_model)
    logger.info(f"教师模型大小: {teacher_size:.2f} MB")
    
    # 创建数据集
    train_dataset, val_dataset, _, _ = create_dual_domain_datasets_for_compression(
        args.annotations, args.original_dir, args.infrared_dir
    )
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    
    # 开始压缩
    compressed_model = compress_domain_model(
        teacher_model, train_loader, val_loader, device, 
        args.compressed_dim, args.epochs, args.lr
    )
    
    # 计算压缩模型大小
    compressed_size = calculate_model_size(compressed_model)
    compression_ratio = teacher_size / compressed_size
    
    logger.info(f"压缩模型大小: {compressed_size:.2f} MB")
    logger.info(f"压缩比: {compression_ratio:.2f}x")
    
    # 保存压缩模型
    torch.save({
        'model_state_dict': compressed_model.state_dict(),
        'base_model_state_dict': base_model.state_dict(),
        'feature_dim': feature_dim,
        'compressed_dim': args.compressed_dim,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat,
        'compressed_domain_adapted': True,
        'teacher_model_path': args.teacher_model,
        'compression_ratio': compression_ratio,
        'teacher_size_mb': teacher_size,
        'compressed_size_mb': compressed_size
    }, args.output)
    
    logger.info(f"压缩模型已保存: {args.output}")

if __name__ == "__main__":
    main()
