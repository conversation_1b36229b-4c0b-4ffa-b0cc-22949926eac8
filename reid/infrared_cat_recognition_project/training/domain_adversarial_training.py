#!/usr/bin/env python3
"""
域对抗训练 (Domain Adversarial Training)
解决红外图像与原始图像之间的域差异问题
"""

import os
import sys
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import random

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image, ImageEnhance
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class GradientReversalLayer(torch.autograd.Function):
    """梯度反转层 - 域对抗训练的核心组件"""
    
    @staticmethod
    def forward(ctx, x, alpha):
        ctx.alpha = alpha
        return x.view_as(x)
    
    @staticmethod
    def backward(ctx, grad_output):
        output = grad_output.neg() * ctx.alpha
        return output, None

class DomainClassifier(nn.Module):
    """域分类器 - 用于区分原始图像和红外图像"""
    
    def __init__(self, feature_dim=2048):
        super(DomainClassifier, self).__init__()
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.BatchNorm1d(feature_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 4, 2)  # 2个域：原始图像(0) vs 红外图像(1)
        )
    
    def forward(self, x, alpha=1.0):
        # 应用梯度反转
        reversed_features = GradientReversalLayer.apply(x, alpha)
        domain_pred = self.classifier(reversed_features)
        return domain_pred

class DomainAdaptationModel(nn.Module):
    """域适应模型 - 结合特征提取器、分类器和域分类器"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=3):
        super(DomainAdaptationModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        
        # 特征增强层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 猫咪分类器
        self.cat_classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
        
        # 域分类器
        self.domain_classifier = DomainClassifier(feature_dim)
    
    def forward(self, x, alpha=1.0, return_features=False):
        # 特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.feature_enhancer(base_features)
        
        if return_features:
            return enhanced_features
        
        # 猫咪分类
        cat_logits = self.cat_classifier(enhanced_features)
        
        # 域分类（带梯度反转）
        domain_logits = self.domain_classifier(enhanced_features, alpha)
        
        return cat_logits, domain_logits, enhanced_features

class DualDomainDataset(Dataset):
    """双域数据集 - 同时包含原始图像和红外图像"""
    
    def __init__(self, original_annotations: Dict, infrared_annotations: Dict, 
                 original_dir: str, infrared_dir: str, cat_to_id: Dict[str, int], 
                 is_training=True, balance_domains=True):
        self.original_dir = original_dir
        self.infrared_dir = infrared_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 处理原始图像数据
        self.original_samples = []
        for filename, annotation in original_annotations.items():
            if annotation['category'] in cat_to_id:
                self.original_samples.append({
                    'filename': filename,
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'domain': 0  # 原始图像域
                })
        
        # 处理红外图像数据
        self.infrared_samples = []
        for filename, annotation in infrared_annotations.items():
            if annotation['category'] in cat_to_id:
                self.infrared_samples.append({
                    'filename': filename,
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'domain': 1  # 红外图像域
                })
        
        # 平衡两个域的样本数量
        if balance_domains:
            min_samples = min(len(self.original_samples), len(self.infrared_samples))
            self.original_samples = random.sample(self.original_samples, min_samples)
            self.infrared_samples = random.sample(self.infrared_samples, min_samples)
        
        # 合并所有样本
        self.all_samples = self.original_samples + self.infrared_samples
        random.shuffle(self.all_samples)
        
        # 数据变换
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomRotation(degrees=10),
                transforms.ColorJitter(brightness=0.2, contrast=0.3, saturation=0.2),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        logger.info(f"双域数据集: 原始图像 {len(self.original_samples)} 个, 红外图像 {len(self.infrared_samples)} 个")
    
    def __len__(self):
        return len(self.all_samples)
    
    def __getitem__(self, idx):
        sample = self.all_samples[idx]
        
        # 根据域选择图像目录
        if sample['domain'] == 0:  # 原始图像
            image_path = os.path.join(self.original_dir, sample['filename'])
        else:  # 红外图像
            image_path = os.path.join(self.infrared_dir, sample['filename'])
        
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 红外图像特殊处理
            if sample['domain'] == 1 and self.is_training and random.random() < 0.3:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(random.uniform(1.1, 1.5))
                
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        image = self.transform(image)
        return image, sample['label'], sample['domain'], sample['category']

def create_dual_domain_datasets(annotations_path, original_dir, infrared_dir, train_ratio=0.8):
    """创建双域数据集"""
    
    # 加载标注
    with open(annotations_path, 'r', encoding='utf-8') as f:
        annotations_dict = json.load(f)
    
    # 创建红外标注映射
    infrared_annotations = {}
    for original_filename, annotation in annotations_dict.items():
        base_name = original_filename.replace('.jpg', '')
        infrared_filename = f"{base_name}_ir.jpg"
        infrared_path = os.path.join(infrared_dir, infrared_filename)
        
        if os.path.exists(infrared_path):
            infrared_annotations[infrared_filename] = annotation
    
    # 创建类别映射
    categories = list(set(ann['category'] for ann in annotations_dict.values()))
    categories = [cat for cat in categories if cat != "无"]
    categories.sort()
    
    cat_to_id = {cat: idx for idx, cat in enumerate(categories)}
    id_to_cat = {idx: cat for cat, idx in cat_to_id.items()}
    
    # 按类别分割数据
    train_original = {}
    val_original = {}
    train_infrared = {}
    val_infrared = {}
    
    for cat in categories:
        # 原始图像
        cat_original = [(k, v) for k, v in annotations_dict.items() if v['category'] == cat]
        random.shuffle(cat_original)
        split_idx = max(1, int(len(cat_original) * train_ratio))
        
        for k, v in cat_original[:split_idx]:
            train_original[k] = v
        for k, v in cat_original[split_idx:]:
            val_original[k] = v
        
        # 红外图像
        cat_infrared = [(k, v) for k, v in infrared_annotations.items() if v['category'] == cat]
        random.shuffle(cat_infrared)
        split_idx = max(1, int(len(cat_infrared) * train_ratio))
        
        for k, v in cat_infrared[:split_idx]:
            train_infrared[k] = v
        for k, v in cat_infrared[split_idx:]:
            val_infrared[k] = v
    
    # 创建数据集
    train_dataset = DualDomainDataset(
        train_original, train_infrared, original_dir, infrared_dir, cat_to_id, True
    )
    val_dataset = DualDomainDataset(
        val_original, val_infrared, original_dir, infrared_dir, cat_to_id, False
    )
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def train_domain_adaptation_model(model, train_loader, val_loader, device, num_epochs=50, lr=1e-4):
    """训练域适应模型"""
    
    logger.info("开始域对抗训练...")
    
    # 损失函数
    cat_criterion = nn.CrossEntropyLoss()
    domain_criterion = nn.CrossEntropyLoss()
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs, eta_min=1e-6)
    
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    for epoch in range(num_epochs):
        model.train()
        total_loss = 0.0
        cat_loss_sum = 0.0
        domain_loss_sum = 0.0
        num_batches = 0
        
        # 动态调整域对抗强度
        p = float(epoch) / num_epochs
        alpha = 2. / (1. + np.exp(-10 * p)) - 1
        
        for images, cat_labels, domain_labels, _ in train_loader:
            images = images.to(device)
            cat_labels = cat_labels.to(device)
            domain_labels = domain_labels.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            cat_logits, domain_logits, features = model(images, alpha)
            
            # 计算损失
            cat_loss = cat_criterion(cat_logits, cat_labels)
            domain_loss = domain_criterion(domain_logits, domain_labels)
            
            # 总损失：猫咪分类损失 + 域对抗损失
            loss = cat_loss + 0.5 * domain_loss
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            cat_loss_sum += cat_loss.item()
            domain_loss_sum += domain_loss.item()
            num_batches += 1
        
        scheduler.step()
        
        # 验证
        if (epoch + 1) % 3 == 0:
            val_acc = evaluate_domain_model(model, train_loader, val_loader, device)
            
            logger.info(f"Epoch {epoch+1}/{num_epochs}")
            logger.info(f"  总损失: {total_loss/num_batches:.4f}")
            logger.info(f"  猫咪分类损失: {cat_loss_sum/num_batches:.4f}")
            logger.info(f"  域对抗损失: {domain_loss_sum/num_batches:.4f}")
            logger.info(f"  域对抗强度α: {alpha:.4f}")
            logger.info(f"  验证准确率: {val_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.2e}")
            
            if val_acc > best_val_acc:
                best_val_acc = val_acc
                patience_counter = 0
                logger.info(f"  新的最佳验证准确率: {best_val_acc:.4f}")
            else:
                patience_counter += 1
                
            if patience_counter >= patience:
                logger.info("早停")
                break
    
    logger.info(f"域对抗训练完成! 最佳验证准确率: {best_val_acc:.4f}")
    return model

def evaluate_domain_model(model, train_loader, val_loader, device):
    """评估域适应模型"""
    
    model.eval()
    
    # 提取训练集特征（只使用原始图像作为参考库）
    train_features = []
    train_labels = []
    
    with torch.no_grad():
        for images, cat_labels, domain_labels, _ in train_loader:
            # 只使用原始图像域的样本作为参考
            original_mask = domain_labels == 0
            if original_mask.sum() > 0:
                original_images = images[original_mask].to(device)
                original_labels = cat_labels[original_mask]
                
                features = model(original_images, return_features=True)
                train_features.append(features.cpu().numpy())
                train_labels.append(original_labels.numpy())
    
    if len(train_features) == 0:
        return 0.0
    
    train_features = np.vstack(train_features)
    train_labels = np.hstack(train_labels)
    
    # 提取验证集特征（只评估红外图像）
    val_features = []
    val_labels = []
    
    with torch.no_grad():
        for images, cat_labels, domain_labels, _ in val_loader:
            # 只评估红外图像域的样本
            infrared_mask = domain_labels == 1
            if infrared_mask.sum() > 0:
                infrared_images = images[infrared_mask].to(device)
                infrared_labels = cat_labels[infrared_mask]
                
                features = model(infrared_images, return_features=True)
                val_features.append(features.cpu().numpy())
                val_labels.append(infrared_labels.numpy())
    
    if len(val_features) == 0:
        return 0.0
    
    val_features = np.vstack(val_features)
    val_labels = np.hstack(val_labels)
    
    # 特征标准化
    scaler = StandardScaler()
    train_features_scaled = scaler.fit_transform(train_features)
    val_features_scaled = scaler.transform(val_features)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(train_features)), metric='cosine')
    knn.fit(train_features_scaled, train_labels)
    
    val_pred = knn.predict(val_features_scaled)
    accuracy = accuracy_score(val_labels, val_pred)
    
    return accuracy

def main():
    parser = argparse.ArgumentParser(description="域对抗训练")
    parser.add_argument("--pretrained", type=str, required=True, help="预训练模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--output", type=str, default="./domain_adapted_model.pth", help="输出模型路径")
    parser.add_argument("--epochs", type=int, default=50, help="训练轮数")
    parser.add_argument("--lr", type=float, default=1e-4, help="学习率")
    parser.add_argument("--batch-size", type=int, default=8, help="批次大小")
    
    args = parser.parse_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建双域数据集
    train_dataset, val_dataset, cat_to_id, id_to_cat = create_dual_domain_datasets(
        args.annotations, args.original_dir, args.infrared_dir
    )
    
    # 创建域适应模型
    model = DomainAdaptationModel(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"类别: {list(cat_to_id.keys())}")
    
    # 开始域对抗训练
    model = train_domain_adaptation_model(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'base_model_state_dict': base_model.state_dict(),
        'feature_dim': feature_dim,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat,
        'domain_adapted': True
    }, args.output)
    
    logger.info(f"域适应模型已保存: {args.output}")

if __name__ == "__main__":
    main()
