#!/usr/bin/env python3
"""
部署优化脚本
将压缩后的域适应模型转换为ONNX格式，并进行量化优化
"""

import os
import sys
import json
import argparse
import logging
import time
from pathlib import Path
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
import onnx
import onnxruntime as ort
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from compress_domain_adapted_model import CompressedDomainAdaptationModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeploymentOptimizer:
    """部署优化器"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        self.model = None
        self.cat_to_id = None
        self.id_to_cat = None
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_model()
    
    def load_model(self):
        """加载压缩模型"""
        logger.info(f"加载压缩模型: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        feature_dim = checkpoint.get('feature_dim', 2048)
        compressed_dim = checkpoint.get('compressed_dim', 512)
        self.cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        self.id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
        
        # 创建基础模型
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['base_model_state_dict'])
        
        # 创建压缩模型
        self.model = CompressedDomainAdaptationModel(
            base_model, feature_dim, compressed_dim, len(self.cat_to_id)
        )
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"模型加载完成，特征维度: {feature_dim} -> {compressed_dim}")
    
    def export_to_onnx(self, output_path: str, opset_version: int = 11) -> str:
        """导出模型为ONNX格式"""
        logger.info(f"开始导出ONNX模型: {output_path}")
        
        # 创建示例输入
        dummy_input = torch.randn(1, 3, 224, 224).to(self.device)
        
        # 导出ONNX
        torch.onnx.export(
            self.model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['logits', 'features'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'logits': {0: 'batch_size'},
                'features': {0: 'batch_size'}
            }
        )
        
        # 验证ONNX模型
        onnx_model = onnx.load(output_path)
        onnx.checker.check_model(onnx_model)
        
        logger.info(f"ONNX模型导出成功: {output_path}")
        return output_path
    
    def quantize_onnx_model(self, onnx_path: str, quantized_path: str) -> str:
        """量化ONNX模型"""
        logger.info(f"开始量化ONNX模型: {quantized_path}")
        
        try:
            from onnxruntime.quantization import quantize_dynamic, QuantType
            
            # 动态量化
            quantize_dynamic(
                onnx_path,
                quantized_path,
                weight_type=QuantType.QUInt8
            )
            
            logger.info(f"量化模型生成成功: {quantized_path}")
            return quantized_path
            
        except ImportError:
            logger.warning("onnxruntime量化功能不可用，跳过量化步骤")
            return None
        except Exception as e:
            logger.error(f"量化失败: {e}")
            return None
    
    def create_inference_wrapper(self, output_dir: str):
        """创建推理包装器"""
        logger.info("创建推理包装器...")
        
        wrapper_code = f'''#!/usr/bin/env python3
"""
红外猫咪识别推理包装器
使用ONNX模型进行快速推理
"""

import os
import json
import time
from typing import Dict, Tuple, List
import numpy as np
from PIL import Image
import onnxruntime as ort
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

class InfraredCatRecognizer:
    """红外猫咪识别器"""
    
    def __init__(self, onnx_model_path: str, reference_features_path: str = None):
        self.onnx_model_path = onnx_model_path
        self.reference_features_path = reference_features_path
        
        # 类别映射
        self.cat_to_id = {json.dumps(self.cat_to_id, ensure_ascii=False)}
        self.id_to_cat = {json.dumps(self.id_to_cat, ensure_ascii=False)}
        
        # 加载ONNX模型
        self.session = ort.InferenceSession(onnx_model_path)
        self.input_name = self.session.get_inputs()[0].name
        
        # 特征标准化器
        self.scaler = StandardScaler()
        self.reference_features = None
        self.reference_labels = None
        
        # 加载参考特征（如果提供）
        if reference_features_path and os.path.exists(reference_features_path):
            self.load_reference_features(reference_features_path)
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """预处理图像"""
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 调整大小
            image = image.resize((224, 224))
            
            # 转换为numpy数组并归一化
            image_array = np.array(image).astype(np.float32) / 255.0
            
            # 标准化
            mean = np.array([0.485, 0.456, 0.406])
            std = np.array([0.229, 0.224, 0.225])
            image_array = (image_array - mean) / std
            
            # 调整维度 (H, W, C) -> (1, C, H, W)
            image_array = np.transpose(image_array, (2, 0, 1))
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
            
        except Exception as e:
            raise ValueError(f"图像预处理失败: {{e}}")
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取图像特征"""
        # 预处理图像
        input_data = self.preprocess_image(image_path)
        
        # ONNX推理
        outputs = self.session.run(None, {{self.input_name: input_data}})
        
        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()
    
    def load_reference_features(self, features_path: str):
        """加载参考特征数据库"""
        with open(features_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.reference_features = np.array(data['features'])
        self.reference_labels = np.array(data['labels'])
        
        # 标准化特征
        self.reference_features = self.scaler.fit_transform(self.reference_features)
    
    def predict(self, image_path: str, k: int = 7) -> Tuple[str, float]:
        """预测图像类别"""
        if self.reference_features is None:
            raise ValueError("未加载参考特征数据库")
        
        # 提取特征
        features = self.extract_features(image_path)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
        knn.fit(self.reference_features, self.reference_labels)
        
        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)
        
        predicted_cat = self.id_to_cat[str(pred_label)]
        
        return predicted_cat, float(confidence)
    
    def batch_predict(self, image_paths: List[str], k: int = 7) -> List[Tuple[str, float]]:
        """批量预测"""
        results = []
        for image_path in image_paths:
            try:
                result = self.predict(image_path, k)
                results.append(result)
            except Exception as e:
                results.append((None, 0.0))
        return results
    
    def benchmark(self, image_paths: List[str], num_runs: int = 10) -> Dict:
        """性能基准测试"""
        if not image_paths:
            raise ValueError("需要提供测试图像路径")
        
        # 预热
        for _ in range(3):
            self.extract_features(image_paths[0])
        
        # 测试特征提取速度
        start_time = time.time()
        for _ in range(num_runs):
            for image_path in image_paths:
                self.extract_features(image_path)
        end_time = time.time()
        
        total_images = len(image_paths) * num_runs
        total_time = end_time - start_time
        throughput = total_images / total_time
        
        return {{
            'total_images': total_images,
            'total_time': total_time,
            'throughput': throughput,
            'avg_time_per_image': total_time / total_images
        }}

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="红外猫咪识别推理")
    parser.add_argument("--model", type=str, required=True, help="ONNX模型路径")
    parser.add_argument("--reference", type=str, help="参考特征数据库路径")
    parser.add_argument("--image", type=str, help="单张图像路径")
    parser.add_argument("--images", type=str, nargs='+', help="多张图像路径")
    parser.add_argument("--benchmark", action='store_true', help="运行性能基准测试")
    
    args = parser.parse_args()
    
    # 创建识别器
    recognizer = InfraredCatRecognizer(args.model, args.reference)
    
    if args.image:
        # 单张图像预测
        cat, confidence = recognizer.predict(args.image)
        print(f"预测结果: {{cat}} (置信度: {{confidence:.4f}})")
    
    elif args.images:
        # 多张图像预测
        results = recognizer.batch_predict(args.images)
        for i, (cat, confidence) in enumerate(results):
            print(f"图像 {{i+1}}: {{cat}} (置信度: {{confidence:.4f}})")
    
    if args.benchmark and (args.image or args.images):
        # 性能基准测试
        test_images = [args.image] if args.image else args.images
        benchmark_results = recognizer.benchmark(test_images)
        print(f"\\n性能基准测试结果:")
        print(f"  总图像数: {{benchmark_results['total_images']}}")
        print(f"  总耗时: {{benchmark_results['total_time']:.2f}} 秒")
        print(f"  吞吐量: {{benchmark_results['throughput']:.2f}} 图像/秒")
        print(f"  平均每张: {{benchmark_results['avg_time_per_image']*1000:.2f}} 毫秒")
'''
        
        # 保存推理包装器
        wrapper_path = os.path.join(output_dir, "infrared_cat_recognizer.py")
        with open(wrapper_path, 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        logger.info(f"推理包装器已保存: {wrapper_path}")
        return wrapper_path
    
    def create_reference_features_database(self, annotations_path: str, original_dir: str, output_path: str):
        """创建参考特征数据库"""
        logger.info("创建参考特征数据库...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 提取原始图像特征
        features_list = []
        labels_list = []
        
        for filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                image_path = os.path.join(original_dir, filename)
                if os.path.exists(image_path):
                    try:
                        # 预处理图像
                        image = Image.open(image_path).convert('RGB')
                        image_tensor = self.transform(image).unsqueeze(0).to(self.device)
                        
                        # 提取特征
                        with torch.no_grad():
                            features = self.model(image_tensor, return_features=True)
                            features_list.append(features.cpu().numpy().flatten())
                            labels_list.append(self.cat_to_id[annotation['category']])
                    except Exception as e:
                        logger.warning(f"处理图像失败 {image_path}: {e}")
        
        # 保存特征数据库
        database = {
            'features': [f.tolist() for f in features_list],
            'labels': labels_list,
            'cat_to_id': self.cat_to_id,
            'id_to_cat': self.id_to_cat
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(database, f, ensure_ascii=False, indent=2)
        
        logger.info(f"参考特征数据库已保存: {output_path}")
        logger.info(f"包含 {len(features_list)} 个特征向量")
        
        return output_path
    
    def get_model_info(self, onnx_path: str = None, quantized_path: str = None) -> Dict:
        """获取模型信息"""
        info = {}
        
        # PyTorch模型信息
        pytorch_size = os.path.getsize(self.model_path) / (1024 * 1024)
        info['pytorch'] = {
            'size_mb': pytorch_size,
            'path': self.model_path
        }
        
        # ONNX模型信息
        if onnx_path and os.path.exists(onnx_path):
            onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
            info['onnx'] = {
                'size_mb': onnx_size,
                'path': onnx_path,
                'compression_ratio': pytorch_size / onnx_size
            }
        
        # 量化模型信息
        if quantized_path and os.path.exists(quantized_path):
            quantized_size = os.path.getsize(quantized_path) / (1024 * 1024)
            info['quantized'] = {
                'size_mb': quantized_size,
                'path': quantized_path,
                'compression_ratio': pytorch_size / quantized_size
            }
        
        return info

def main():
    parser = argparse.ArgumentParser(description="部署优化")
    parser.add_argument("--model", type=str, required=True, help="压缩模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--output-dir", type=str, default="./deployment", help="输出目录")
    parser.add_argument("--opset-version", type=int, default=11, help="ONNX opset版本")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建部署优化器
    optimizer = DeploymentOptimizer(args.model, args.device)
    
    # 导出ONNX模型
    onnx_path = output_dir / "infrared_cat_model.onnx"
    optimizer.export_to_onnx(str(onnx_path), args.opset_version)
    
    # 量化ONNX模型
    quantized_path = output_dir / "infrared_cat_model_quantized.onnx"
    quantized_result = optimizer.quantize_onnx_model(str(onnx_path), str(quantized_path))
    
    # 创建参考特征数据库
    features_db_path = output_dir / "reference_features.json"
    optimizer.create_reference_features_database(
        args.annotations, args.original_dir, str(features_db_path)
    )
    
    # 创建推理包装器
    wrapper_path = optimizer.create_inference_wrapper(str(output_dir))
    
    # 获取模型信息
    model_info = optimizer.get_model_info(str(onnx_path), str(quantized_path) if quantized_result else None)
    
    # 保存部署信息
    deployment_info = {
        'models': model_info,
        'files': {
            'onnx_model': str(onnx_path),
            'quantized_model': str(quantized_path) if quantized_result else None,
            'reference_features': str(features_db_path),
            'inference_wrapper': wrapper_path
        },
        'usage': {
            'single_image': f"python {wrapper_path} --model {onnx_path} --reference {features_db_path} --image <image_path>",
            'batch_images': f"python {wrapper_path} --model {onnx_path} --reference {features_db_path} --images <image1> <image2> ...",
            'benchmark': f"python {wrapper_path} --model {onnx_path} --reference {features_db_path} --image <image_path> --benchmark"
        }
    }
    
    deployment_info_path = output_dir / "deployment_info.json"
    with open(deployment_info_path, 'w', encoding='utf-8') as f:
        json.dump(deployment_info, f, ensure_ascii=False, indent=2)
    
    logger.info(f"部署优化完成!")
    logger.info(f"输出目录: {output_dir}")
    logger.info(f"部署信息: {deployment_info_path}")
    
    # 打印总结
    print("\n" + "="*60)
    print("部署优化总结")
    print("="*60)
    print(f"PyTorch模型大小: {model_info['pytorch']['size_mb']:.2f} MB")
    if 'onnx' in model_info:
        print(f"ONNX模型大小: {model_info['onnx']['size_mb']:.2f} MB")
        print(f"ONNX压缩比: {model_info['onnx']['compression_ratio']:.2f}x")
    if 'quantized' in model_info:
        print(f"量化模型大小: {model_info['quantized']['size_mb']:.2f} MB")
        print(f"量化压缩比: {model_info['quantized']['compression_ratio']:.2f}x")
    print(f"部署文件已保存到: {output_dir}")
    print("="*60)

if __name__ == "__main__":
    main()
