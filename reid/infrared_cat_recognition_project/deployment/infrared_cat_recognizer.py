#!/usr/bin/env python3
"""
红外猫咪识别推理包装器
使用ONNX模型进行快速推理
"""

import os
import json
import time
from typing import Dict, <PERSON><PERSON>, List
import numpy as np
from PIL import Image
import onnxruntime as ort
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

class InfraredCatRecognizer:
    """红外猫咪识别器"""
    
    def __init__(self, onnx_model_path: str, reference_features_path: str = None):
        self.onnx_model_path = onnx_model_path
        self.reference_features_path = reference_features_path
        
        # 类别映射
        self.cat_to_id = {"小白": 0, "小花": 1, "小黑": 2}
        self.id_to_cat = {"0": "小白", "1": "小花", "2": "小黑"}
        
        # 加载ONNX模型
        self.session = ort.InferenceSession(onnx_model_path)
        self.input_name = self.session.get_inputs()[0].name
        
        # 特征标准化器
        self.scaler = StandardScaler()
        self.reference_features = None
        self.reference_labels = None
        
        # 加载参考特征（如果提供）
        if reference_features_path and os.path.exists(reference_features_path):
            self.load_reference_features(reference_features_path)
    
    def preprocess_image(self, image_path: str) -> np.ndarray:
        """预处理图像"""
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')
            
            # 调整大小
            image = image.resize((224, 224))
            
            # 转换为numpy数组并归一化
            image_array = np.array(image, dtype=np.float32) / 255.0
            
            # 标准化
            mean = np.array([0.485, 0.456, 0.406], dtype=np.float32)
            std = np.array([0.229, 0.224, 0.225], dtype=np.float32)
            image_array = (image_array - mean) / std
            
            # 调整维度 (H, W, C) -> (1, C, H, W)
            image_array = np.transpose(image_array, (2, 0, 1))
            image_array = np.expand_dims(image_array, axis=0)
            
            return image_array
            
        except Exception as e:
            raise ValueError(f"图像预处理失败: {e}")
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取图像特征"""
        # 预处理图像
        input_data = self.preprocess_image(image_path)
        
        # ONNX推理
        outputs = self.session.run(None, {self.input_name: input_data})
        
        # 返回特征向量
        features = outputs[1]  # features是第二个输出
        return features.flatten()
    
    def load_reference_features(self, features_path: str):
        """加载参考特征数据库"""
        with open(features_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.reference_features = np.array(data['features'])
        self.reference_labels = np.array(data['labels'])
        
        # 标准化特征
        self.reference_features = self.scaler.fit_transform(self.reference_features)
    
    def predict(self, image_path: str, k: int = 7) -> Tuple[str, float]:
        """预测图像类别"""
        if self.reference_features is None:
            raise ValueError("未加载参考特征数据库")
        
        # 提取特征
        features = self.extract_features(image_path)
        features_scaled = self.scaler.transform(features.reshape(1, -1))
        
        # KNN分类
        knn = KNeighborsClassifier(n_neighbors=min(k, len(self.reference_features)), metric='cosine')
        knn.fit(self.reference_features, self.reference_labels)
        
        # 预测
        pred_label = knn.predict(features_scaled)[0]
        pred_proba = knn.predict_proba(features_scaled)[0]
        confidence = max(pred_proba)
        
        predicted_cat = self.id_to_cat[str(pred_label)]
        
        return predicted_cat, float(confidence)
    
    def batch_predict(self, image_paths: List[str], k: int = 7) -> List[Tuple[str, float]]:
        """批量预测"""
        results = []
        for image_path in image_paths:
            try:
                result = self.predict(image_path, k)
                results.append(result)
            except Exception as e:
                results.append((None, 0.0))
        return results
    
    def benchmark(self, image_paths: List[str], num_runs: int = 10) -> Dict:
        """性能基准测试"""
        if not image_paths:
            raise ValueError("需要提供测试图像路径")
        
        # 预热
        for _ in range(3):
            self.extract_features(image_paths[0])
        
        # 测试特征提取速度
        start_time = time.time()
        for _ in range(num_runs):
            for image_path in image_paths:
                self.extract_features(image_path)
        end_time = time.time()
        
        total_images = len(image_paths) * num_runs
        total_time = end_time - start_time
        throughput = total_images / total_time
        
        return {
            'total_images': total_images,
            'total_time': total_time,
            'throughput': throughput,
            'avg_time_per_image': total_time / total_images
        }

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="红外猫咪识别推理")
    parser.add_argument("--model", type=str, required=True, help="ONNX模型路径")
    parser.add_argument("--reference", type=str, help="参考特征数据库路径")
    parser.add_argument("--image", type=str, help="单张图像路径")
    parser.add_argument("--images", type=str, nargs='+', help="多张图像路径")
    parser.add_argument("--benchmark", action='store_true', help="运行性能基准测试")
    
    args = parser.parse_args()
    
    # 创建识别器
    recognizer = InfraredCatRecognizer(args.model, args.reference)
    
    if args.image:
        # 单张图像预测
        cat, confidence = recognizer.predict(args.image)
        print(f"预测结果: {cat} (置信度: {confidence:.4f})")
    
    elif args.images:
        # 多张图像预测
        results = recognizer.batch_predict(args.images)
        for i, (cat, confidence) in enumerate(results):
            print(f"图像 {i+1}: {cat} (置信度: {confidence:.4f})")
    
    if args.benchmark and (args.image or args.images):
        # 性能基准测试
        test_images = [args.image] if args.image else args.images
        benchmark_results = recognizer.benchmark(test_images)
        print(f"\n性能基准测试结果:")
        print(f"  总图像数: {benchmark_results['total_images']}")
        print(f"  总耗时: {benchmark_results['total_time']:.2f} 秒")
        print(f"  吞吐量: {benchmark_results['throughput']:.2f} 图像/秒")
        print(f"  平均每张: {benchmark_results['avg_time_per_image']*1000:.2f} 毫秒")
