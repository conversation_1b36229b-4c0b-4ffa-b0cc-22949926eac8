# 测试指南

## 概述

本指南介绍了红外图像猫咪识别系统的各种测试方法，包括准确率测试、性能测试、随机验证测试和模型分析。

## 测试环境准备

### 依赖安装

```bash
pip install torch torchvision
pip install scikit-learn
pip install pillow
pip install numpy
pip install matplotlib  # 用于结果可视化
```

### 测试数据准备

确保有以下数据：
- 原始图像目录
- 红外图像目录  
- 标注文件
- 训练好的模型文件

## 测试类型

### 1. 压缩模型对比测试

测试压缩模型与教师模型的性能对比。

#### 1.1 运行命令

```bash
cd testing/

python test_compressed_model.py \
    --compressed-model ../models/compressed_domain_model.pth \
    --teacher-model ../models/domain_adapted_model.pth \
    --annotations ../../tagging/annotations.json \
    --original-dir ../../dataset/renamed_thumbnails \
    --infrared-dir ../../dataset/ir_thumbnails \
    --accuracy-samples 200 \
    --speed-samples 100 \
    --output compressed_model_test_results.json
```

#### 1.2 测试内容

- **准确率对比**: 压缩模型 vs 教师模型
- **速度对比**: 推理吞吐量测试
- **模型大小对比**: 文件大小和参数数量

#### 1.3 预期结果

```json
{
  "accuracy_comparison": {
    "compressed": {"accuracy": 1.0},
    "teacher": {"accuracy": 1.0},
    "accuracy_difference": 0.0
  },
  "speed_comparison": {
    "compressed": {"throughput": 59.52},
    "teacher": {"throughput": 62.79}
  }
}
```

### 2. 随机验证测试

进行多轮随机数据分割测试，验证模型的稳定性和泛化能力。

#### 2.1 运行命令

```bash
python random_validation_test.py \
    --model ../models/domain_adapted_model.pth \
    --annotations ../../tagging/annotations.json \
    --original-dir ../../dataset/renamed_thumbnails \
    --infrared-dir ../../dataset/ir_thumbnails \
    --num-rounds 20 \
    --test-ratio 0.3 \
    --output random_validation_results.json
```

#### 2.2 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--num-rounds` | 20 | 随机测试轮数 |
| `--test-ratio` | 0.3 | 测试集比例 |
| `--device` | auto | 计算设备 |

#### 2.3 测试流程

1. **数据分割**: 按类别进行分层随机采样
2. **特征提取**: 使用原始图像构建参考库
3. **红外测试**: 测试红外图像识别准确率
4. **统计分析**: 计算多轮测试的统计指标

#### 2.4 结果解读

```
随机验证测试总结
============================================================
测试轮数: 20
测试集比例: 30.0%
平均准确率: 0.9967 ± 0.0017
准确率范围: 0.9934 - 1.0000
100%准确率轮数: 1/20
95%+准确率轮数: 20/20
90%+准确率轮数: 20/20
```

**关键指标**：
- **平均准确率**: 应该 > 95%
- **标准差**: 应该 < 5%
- **最低准确率**: 应该 > 90%

### 3. 模型性能分析

深入分析模型的性能瓶颈和各层耗时。

#### 3.1 运行命令

```bash
python analyze_model_performance.py \
    --teacher-model ../models/domain_adapted_model.pth \
    --compressed-model ../models/compressed_domain_model.pth \
    --output model_performance_analysis.json
```

#### 3.2 分析内容

1. **模型结构对比**
   - 参数数量
   - 模型大小
   - 各组件参数分布

2. **推理速度基准**
   - 纯推理性能
   - 吞吐量对比
   - 延迟分析

3. **各层性能分析**
   - 特征提取耗时
   - 特征处理耗时
   - 分类耗时

4. **内存使用分析**
   - GPU内存占用
   - 内存效率对比

#### 3.3 结果示例

```
模型性能分析总结
============================================================
模型结构对比:
  教师模型参数: 99,942,271
  压缩模型参数: 89,569,661
  参数压缩比: 1.12x

推理速度对比:
  教师模型吞吐量: 101.30 图像/秒
  压缩模型吞吐量: 104.61 图像/秒
  速度比: 1.03x

各层耗时对比:
  教师模型 feature_extraction: 9.14 ms
  压缩模型 feature_extraction: 9.47 ms
```

## 部署模型测试

### 4. ONNX模型测试

测试量化ONNX模型的性能。

#### 4.1 单张图像测试

```bash
cd ../deployment/

python infrared_cat_recognizer.py \
    --model infrared_cat_model_quantized.onnx \
    --reference reference_features.json \
    --image path/to/test_image.jpg
```

#### 4.2 批量测试

```bash
python infrared_cat_recognizer.py \
    --model infrared_cat_model_quantized.onnx \
    --reference reference_features.json \
    --images image1.jpg image2.jpg image3.jpg
```

#### 4.3 性能基准测试

```bash
python infrared_cat_recognizer.py \
    --model infrared_cat_model_quantized.onnx \
    --reference reference_features.json \
    --image test_image.jpg \
    --benchmark
```

预期输出：
```
预测结果: 小花 (置信度: 1.0000)

性能基准测试结果:
  总图像数: 10
  总耗时: 0.51 秒
  吞吐量: 19.64 图像/秒
  平均每张: 50.92 毫秒
```

## 测试最佳实践

### 数据准备

1. **测试集独立性**: 确保测试数据未参与训练
2. **数据多样性**: 包含不同光照、角度的图像
3. **标注准确性**: 仔细检查测试数据标注

### 测试策略

1. **多轮测试**: 进行多次随机测试确保稳定性
2. **分层测试**: 按类别分别测试性能
3. **边界测试**: 测试模糊、困难样本

### 性能基准

| 指标 | 优秀 | 良好 | 需改进 |
|------|------|------|--------|
| 准确率 | >99% | >95% | <95% |
| 稳定性(标准差) | <2% | <5% | >5% |
| 推理速度 | >50 fps | >20 fps | <20 fps |

## 问题诊断

### 准确率问题

#### 症状：准确率低于预期
**可能原因**：
1. 数据质量问题
2. 模型欠拟合
3. 域适应不充分

**解决方案**：
1. 检查数据对齐
2. 增加训练轮数
3. 调整域对抗权重

#### 症状：不同类别性能差异大
**可能原因**：
1. 数据不平衡
2. 类别特征相似度高

**解决方案**：
1. 平衡训练数据
2. 增强数据增强
3. 调整损失函数权重

### 性能问题

#### 症状：推理速度慢
**可能原因**：
1. 模型过大
2. 特征维度过高
3. KNN计算瓶颈

**解决方案**：
1. 使用压缩模型
2. 降低特征维度
3. 优化KNN算法

#### 症状：内存占用高
**可能原因**：
1. 批次大小过大
2. 特征缓存过多

**解决方案**：
1. 减小批次大小
2. 清理无用缓存

## 测试报告模板

### 基本信息
- 测试日期：
- 模型版本：
- 测试数据集：
- 测试环境：

### 测试结果
- 准确率：
- 推理速度：
- 内存占用：
- 稳定性：

### 问题记录
- 发现的问题：
- 解决方案：
- 改进建议：

## 自动化测试

### 测试脚本

```bash
#!/bin/bash
# 自动化测试脚本

echo "开始模型测试..."

# 1. 压缩模型测试
python test_compressed_model.py \
    --compressed-model ../models/compressed_domain_model.pth \
    --teacher-model ../models/domain_adapted_model.pth \
    --annotations ../../tagging/annotations.json \
    --original-dir ../../dataset/renamed_thumbnails \
    --infrared-dir ../../dataset/ir_thumbnails

# 2. 随机验证测试
python random_validation_test.py \
    --model ../models/domain_adapted_model.pth \
    --annotations ../../tagging/annotations.json \
    --original-dir ../../dataset/renamed_thumbnails \
    --infrared-dir ../../dataset/ir_thumbnails \
    --num-rounds 10

# 3. 性能分析
python analyze_model_performance.py \
    --teacher-model ../models/domain_adapted_model.pth \
    --compressed-model ../models/compressed_domain_model.pth

echo "测试完成！"
```

### CI/CD集成

可以将测试脚本集成到CI/CD流水线中，实现自动化测试和质量保证。

## 总结

通过系统性的测试，我们验证了：

1. **模型准确性**: 100%红外图像识别准确率
2. **模型稳定性**: 20轮随机测试平均99.67%准确率
3. **压缩效果**: 零性能损失的模型压缩
4. **部署就绪**: ONNX量化模型可直接部署

测试是确保模型质量的关键环节，建议在每次模型更新后都进行完整的测试流程。
