# 部署指南

## 概述

本指南介绍如何将训练好的红外图像猫咪识别模型部署到生产环境，包括ONNX转换、量化优化和推理服务搭建。

## 部署架构

```
生产环境部署架构
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   红外摄像头     │───▶│   图像预处理     │───▶│   特征提取       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   识别结果       │◀───│   KNN分类       │◀───│   特征标准化     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 环境要求

### 最低要求
- **CPU**: 2核心以上
- **内存**: 4GB RAM
- **存储**: 1GB可用空间
- **Python**: 3.8+

### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM
- **GPU**: 可选，用于加速推理
- **存储**: 2GB可用空间

### 依赖安装

```bash
# 基础依赖
pip install onnxruntime
pip install pillow
pip install numpy
pip install scikit-learn

# GPU加速（可选）
pip install onnxruntime-gpu
```

## 模型部署

### 1. 模型文件准备

确保有以下文件：

```
deployment/
├── infrared_cat_model_quantized.onnx    # 量化ONNX模型
├── reference_features.json              # 参考特征数据库
├── infrared_cat_recognizer.py           # 推理包装器
└── deployment_info.json                 # 部署配置
```

### 2. 快速部署测试

```bash
cd deployment/

# 测试单张图像
python infrared_cat_recognizer.py \
    --model infrared_cat_model_quantized.onnx \
    --reference reference_features.json \
    --image test_image.jpg

# 预期输出
# 预测结果: 小花 (置信度: 1.0000)
```

### 3. 性能基准测试

```bash
# 运行性能基准测试
python infrared_cat_recognizer.py \
    --model infrared_cat_model_quantized.onnx \
    --reference reference_features.json \
    --image test_image.jpg \
    --benchmark

# 预期输出
# 性能基准测试结果:
#   总图像数: 10
#   总耗时: 0.51 秒
#   吞吐量: 19.64 图像/秒
#   平均每张: 50.92 毫秒
```

## API服务部署

### 1. Flask Web服务

创建简单的Web API服务：

```python
# app.py
from flask import Flask, request, jsonify
import os
from infrared_cat_recognizer import InfraredCatRecognizer

app = Flask(__name__)

# 初始化识别器
recognizer = InfraredCatRecognizer(
    'infrared_cat_model_quantized.onnx',
    'reference_features.json'
)

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # 获取上传的图像
        if 'image' not in request.files:
            return jsonify({'error': 'No image provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image selected'}), 400
        
        # 保存临时文件
        temp_path = f'/tmp/{file.filename}'
        file.save(temp_path)
        
        # 进行预测
        cat, confidence = recognizer.predict(temp_path)
        
        # 清理临时文件
        os.remove(temp_path)
        
        return jsonify({
            'cat': cat,
            'confidence': float(confidence),
            'status': 'success'
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health():
    return jsonify({'status': 'healthy'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### 2. 启动服务

```bash
# 安装Flask
pip install flask

# 启动服务
python app.py

# 服务将在 http://localhost:5000 启动
```

### 3. API使用示例

```bash
# 使用curl测试API
curl -X POST \
  http://localhost:5000/predict \
  -F "image=@test_image.jpg"

# 响应示例
{
  "cat": "小花",
  "confidence": 1.0,
  "status": "success"
}
```

## Docker部署

### 1. Dockerfile

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用文件
COPY . .

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "app.py"]
```

### 2. requirements.txt

```txt
onnxruntime==1.16.0
pillow==10.0.0
numpy==1.24.3
scikit-learn==1.3.0
flask==2.3.2
```

### 3. 构建和运行

```bash
# 构建Docker镜像
docker build -t infrared-cat-recognition .

# 运行容器
docker run -p 5000:5000 infrared-cat-recognition

# 后台运行
docker run -d -p 5000:5000 --name cat-recognition infrared-cat-recognition
```

## 生产环境优化

### 1. 性能优化

#### 批处理推理
```python
def batch_predict(self, image_paths: List[str], batch_size: int = 8):
    """批量预测优化"""
    results = []
    for i in range(0, len(image_paths), batch_size):
        batch_paths = image_paths[i:i+batch_size]
        batch_results = self.process_batch(batch_paths)
        results.extend(batch_results)
    return results
```

#### 特征缓存
```python
from functools import lru_cache

@lru_cache(maxsize=1000)
def extract_features_cached(self, image_path: str):
    """带缓存的特征提取"""
    return self.extract_features(image_path)
```

### 2. 监控和日志

```python
import logging
import time
from functools import wraps

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cat_recognition.log'),
        logging.StreamHandler()
    ]
)

def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logging.info(f"{func.__name__} completed in {duration:.3f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logging.error(f"{func.__name__} failed after {duration:.3f}s: {e}")
            raise
    return wrapper
```

### 3. 负载均衡

使用Nginx进行负载均衡：

```nginx
# nginx.conf
upstream cat_recognition {
    server 127.0.0.1:5000;
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://cat_recognition;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 边缘设备部署

### 1. Raspberry Pi部署

```bash
# 在Raspberry Pi上安装依赖
sudo apt-get update
sudo apt-get install python3-pip
pip3 install onnxruntime
pip3 install pillow numpy scikit-learn

# 复制模型文件
scp -r deployment/ pi@raspberry-pi:/home/<USER>/cat-recognition/

# 运行服务
cd /home/<USER>/cat-recognition/
python3 infrared_cat_recognizer.py --model infrared_cat_model_quantized.onnx
```

### 2. 移动端部署

对于移动端部署，可以考虑：

1. **模型进一步量化**: INT8量化
2. **模型剪枝**: 移除不重要的连接
3. **知识蒸馏**: 训练更小的学生模型

## 部署检查清单

### 部署前检查

- [ ] 模型文件完整性验证
- [ ] 依赖环境安装确认
- [ ] 测试数据准备
- [ ] 性能基准测试
- [ ] 安全配置检查

### 部署后验证

- [ ] 服务健康检查
- [ ] API功能测试
- [ ] 性能监控设置
- [ ] 日志系统配置
- [ ] 错误处理测试

### 监控指标

- **准确率**: 实时识别准确率
- **响应时间**: API响应延迟
- **吞吐量**: 每秒处理图像数
- **错误率**: 失败请求比例
- **资源使用**: CPU、内存使用率

## 故障排除

### 常见问题

#### 1. ONNX模型加载失败
**症状**: `onnxruntime.capi.onnxruntime_pybind11_state.RuntimeException`

**解决方案**:
```bash
# 检查ONNX版本兼容性
pip install onnxruntime==1.16.0

# 验证模型文件
python -c "import onnx; onnx.checker.check_model('model.onnx')"
```

#### 2. 推理速度慢
**症状**: 推理时间超过预期

**解决方案**:
1. 使用量化模型
2. 启用多线程推理
3. 批处理优化

#### 3. 内存占用高
**症状**: 内存使用持续增长

**解决方案**:
1. 清理特征缓存
2. 减小批次大小
3. 定期重启服务

### 调试工具

```python
# 性能分析
import cProfile
import pstats

def profile_inference():
    profiler = cProfile.Profile()
    profiler.enable()
    
    # 运行推理
    result = recognizer.predict('test_image.jpg')
    
    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(10)
```

## 安全考虑

### 1. 输入验证

```python
def validate_image(file):
    """验证上传的图像文件"""
    # 检查文件类型
    allowed_types = ['image/jpeg', 'image/png', 'image/jpg']
    if file.content_type not in allowed_types:
        raise ValueError("不支持的文件类型")
    
    # 检查文件大小
    if len(file.read()) > 10 * 1024 * 1024:  # 10MB
        raise ValueError("文件过大")
    
    file.seek(0)  # 重置文件指针
```

### 2. 访问控制

```python
from functools import wraps
from flask import request, abort

def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key')
        if not api_key or api_key != 'your-secret-key':
            abort(401)
        return f(*args, **kwargs)
    return decorated_function

@app.route('/predict', methods=['POST'])
@require_api_key
def predict():
    # 预测逻辑
    pass
```

## 总结

通过本部署指南，您可以：

1. **快速部署**: 使用ONNX量化模型进行高效推理
2. **API服务**: 构建RESTful API服务
3. **容器化**: 使用Docker进行标准化部署
4. **生产优化**: 实现监控、日志和负载均衡
5. **边缘部署**: 在资源受限设备上运行

部署的关键成功因素：
- 选择合适的模型格式（ONNX量化）
- 实施适当的性能优化
- 建立完善的监控体系
- 确保系统安全性

详细的技术实现请参考：
- [训练指南](training_guide.md)
- [测试指南](testing_guide.md)
- [技术报告](technical_report.md)

---

**红外图像猫咪识别系统已准备就绪，可直接用于生产环境部署！** 🚀
