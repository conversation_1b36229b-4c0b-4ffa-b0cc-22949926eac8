{"models": {"domain_adapted_model.pth": {"description": "域对抗训练模型 - 主要模型", "type": "Domain Adaptation Model", "accuracy": {"infrared_images": "100.00%", "original_images": "100.00%", "domain_difference": "0.0000"}, "performance": {"throughput": "62.79 images/sec", "avg_time_per_image": "15.93 ms"}, "model_size": {"file_size_mb": 382.46, "parameters": 99942271, "feature_dimension": 2048}, "architecture": {"feature_extractor": "MegaDescriptor-T-224 based", "feature_enhancer": "Multi-layer MLP with attention", "cat_classifier": "3-class classifier", "domain_classifier": "2-class domain discriminator"}, "training_details": {"epochs": 50, "learning_rate": "1e-4", "batch_size": 8, "domain_adaptation_strength": "Dynamic (0.1 -> 1.0)", "training_data": "1512 image pairs (original + infrared)"}, "usage": {"load_command": "torch.load('domain_adapted_model.pth')", "input_format": "RGB images, 224x224", "output_format": "2048-dim feature vectors + class logits", "supported_classes": ["小白", "小花", "小黑"]}}, "compressed_domain_model.pth": {"description": "知识蒸馏压缩模型", "type": "Compressed Domain Adaptation Model", "accuracy": {"infrared_images": "100.00%", "original_images": "100.00%", "compression_loss": "0.00%"}, "performance": {"throughput": "59.52 images/sec", "avg_time_per_image": "16.80 ms"}, "model_size": {"file_size_mb": 342.85, "parameters": 89569661, "feature_dimension": 512, "compression_ratio": "1.12x"}, "architecture": {"feature_extractor": "Same as teacher model", "feature_compressor": "2048 -> 512 compression network", "cat_classifier": "Lightweight 3-class classifier"}, "training_details": {"method": "Knowledge Distillation", "teacher_model": "domain_adapted_model.pth", "epochs": 30, "learning_rate": "1e-4", "distillation_temperature": 4.0, "loss_weights": "hard_loss: 0.3, distill_loss: 0.7"}, "usage": {"load_command": "torch.load('compressed_domain_model.pth')", "input_format": "RGB images, 224x224", "output_format": "512-dim feature vectors + class logits", "supported_classes": ["小白", "小花", "小黑"]}}}, "deployment_models": {"infrared_cat_model.onnx": {"description": "ONNX格式模型 - 跨平台部署", "location": "../deployment/infrared_cat_model.onnx", "type": "ONNX Model", "size_mb": 344.74, "compression_ratio": "0.99x vs PyTorch", "usage": "onnxruntime inference"}, "infrared_cat_model_quantized.onnx": {"description": "量化ONNX模型 - 生产部署推荐", "location": "../deployment/infrared_cat_model_quantized.onnx", "type": "Quantized ONNX Model", "size_mb": 89.3, "compression_ratio": "3.83x vs PyTorch", "performance": {"throughput": "19.64 images/sec", "avg_time_per_image": "50.92 ms"}, "usage": "onnxruntime inference with quantization"}}, "validation_results": {"random_validation_test": {"test_rounds": 20, "test_ratio": "30%", "average_accuracy": "99.67% ± 0.17%", "accuracy_range": "99.34% - 100.00%", "rounds_with_100_percent": "1/20", "rounds_with_95_percent": "20/20", "category_performance": {"小白": {"accuracy": "99.78% ± 0.28%", "range": "99.44% - 100.00%"}, "小花": {"accuracy": "99.29% ± 0.55%", "range": "98.08% - 100.00%"}, "小黑": {"accuracy": "100.00% ± 0.00%", "range": "100.00% - 100.00%"}}}}, "technical_specifications": {"base_architecture": "MegaDescriptor-T-224", "feature_extraction": "Swin Transformer backbone", "domain_adaptation": "Gradient Reversal Layer", "similarity_metric": "Cosine similarity with KNN (k=7)", "preprocessing": {"resize": "224x224", "normalization": "ImageNet stats", "augmentation": "Random horizontal flip, color jitter"}, "hardware_requirements": {"minimum": "4GB RAM, CPU", "recommended": "8GB RAM, CUDA GPU", "inference": "2GB RAM sufficient for deployment"}}, "usage_examples": {"load_domain_adapted_model": {"code": "checkpoint = torch.load('domain_adapted_model.pth')\nmodel = DomainAdaptationModel(...)\nmodel.load_state_dict(checkpoint['model_state_dict'])"}, "load_compressed_model": {"code": "checkpoint = torch.load('compressed_domain_model.pth')\nmodel = CompressedDomainAdaptationModel(...)\nmodel.load_state_dict(checkpoint['model_state_dict'])"}, "onnx_inference": {"code": "session = ort.InferenceSession('infrared_cat_model_quantized.onnx')\noutputs = session.run(None, {'input': image_array})"}}, "version_info": {"project_version": "1.0.0", "creation_date": "2025-07-11", "pytorch_version": "Compatible with PyTorch 1.8+", "onnx_version": "ONNX opset 13", "python_version": "Python 3.8+"}}