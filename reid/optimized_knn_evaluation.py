#!/usr/bin/env python3
"""
优化的KNN评估 - 基于最佳配置进行进一步优化
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from contrastive_learning_enhancement import ContrastiveProgressiveModel
from enhanced_progressive_training import EnhancedProgressiveDataset

def load_model_safely(model_path: str, device: torch.device):
    """安全加载模型"""
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 33)
    
    # 创建基础特征提取器
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    
    # 根据模型类型创建相应的模型
    if 'projection_head.0.weight' in checkpoint['model_state_dict']:
        # 对比学习模型
        model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
    elif 'few_shot_enhancer.0.weight' in checkpoint['model_state_dict']:
        # Few-Shot模型
        from few_shot_meta_learning import PrototypicalNetwork
        model = PrototypicalNetwork(base_model, feature_dim)
    else:
        print(f"未知模型类型，使用对比学习模型")
        model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    return model, checkpoint

def extract_features(model, dataloader, device):
    """提取特征"""
    
    all_features = []
    all_labels = []
    all_is_original = []
    all_categories = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in dataloader:
            images = images.to(device)
            
            # 根据模型类型提取特征
            if hasattr(model, 'forward') and 'return_features' in model.forward.__code__.co_varnames:
                features = model(images, return_features=True)
            else:
                features = model(images)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
            all_categories.extend(categories)
    
    return (np.vstack(all_features), np.array(all_labels), 
            np.array(all_is_original), all_categories)

def optimized_knn_evaluation(features, labels, is_original, test_name=""):
    """优化的KNN评估"""
    
    print(f"\n🔍 {test_name}KNN优化评估")
    
    # 扩展的KNN配置搜索
    knn_configs = []
    
    # 基础配置
    for k in [3, 5, 7, 9, 11, 13, 15]:
        for metric in ['cosine', 'euclidean', 'manhattan']:
            for weights in ['uniform', 'distance']:
                knn_configs.append({
                    'n_neighbors': min(k, len(set(labels)) - 1),
                    'metric': metric,
                    'weights': weights
                })
    
    best_result = None
    best_individual_acc = 0.0
    best_config = None
    
    print(f"  测试 {len(knn_configs)} 种KNN配置...")
    
    for i, config in enumerate(knn_configs):
        try:
            knn = KNeighborsClassifier(**config)
            
            # 留一法交叉验证
            predictions = []
            for j in range(len(features)):
                train_mask = np.ones(len(features), dtype=bool)
                train_mask[j] = False
                
                if train_mask.sum() > 0:
                    knn.fit(features[train_mask], labels[train_mask])
                    pred = knn.predict(features[j:j+1])
                    predictions.append(pred[0])
                else:
                    predictions.append(labels[j])
            
            predictions = np.array(predictions)
            
            # 计算准确率
            overall_acc = accuracy_score(labels, predictions)
            
            original_mask = is_original.astype(bool)
            original_acc = accuracy_score(labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
            
            individual_mask = ~original_mask
            individual_acc = accuracy_score(labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
            
            # 选择个体准确率最高的配置
            if individual_acc > best_individual_acc:
                best_individual_acc = individual_acc
                best_result = {
                    'overall_acc': overall_acc,
                    'original_acc': original_acc,
                    'individual_acc': individual_acc
                }
                best_config = config
                
        except Exception as e:
            continue
    
    if best_result:
        print(f"  🏆 最佳配置: {best_config}")
        print(f"  📊 最佳结果:")
        print(f"    总体准确率: {best_result['overall_acc']:.4f} ({best_result['overall_acc']*100:.2f}%)")
        print(f"    原始3猫准确率: {best_result['original_acc']:.4f} ({best_result['original_acc']*100:.2f}%)")
        print(f"    个体猫咪准确率: {best_result['individual_acc']:.4f} ({best_result['individual_acc']*100:.2f}%)")
        
        return best_result, best_config
    else:
        print("  ❌ 所有配置都失败了")
        return None, None

def feature_enhancement_evaluation(features, labels, is_original):
    """特征增强评估"""
    
    print(f"\n🚀 特征增强评估")
    
    results = {}
    
    # 1. 原始特征
    print("  测试原始特征...")
    result, config = optimized_knn_evaluation(features, labels, is_original, "原始特征 ")
    if result:
        results['original'] = {'result': result, 'config': config}
    
    # 2. L2归一化特征
    print("  测试L2归一化特征...")
    normalized_features = features / np.linalg.norm(features, axis=1, keepdims=True)
    result, config = optimized_knn_evaluation(normalized_features, labels, is_original, "L2归一化 ")
    if result:
        results['l2_normalized'] = {'result': result, 'config': config}
    
    # 3. 标准化特征
    print("  测试标准化特征...")
    scaler = StandardScaler()
    standardized_features = scaler.fit_transform(features)
    result, config = optimized_knn_evaluation(standardized_features, labels, is_original, "标准化 ")
    if result:
        results['standardized'] = {'result': result, 'config': config}
    
    # 4. PCA降维特征
    if features.shape[1] > 512:
        print("  测试PCA降维特征...")
        pca = PCA(n_components=512, random_state=42)
        pca_features = pca.fit_transform(features)
        result, config = optimized_knn_evaluation(pca_features, labels, is_original, "PCA降维 ")
        if result:
            results['pca'] = {'result': result, 'config': config}
    
    # 5. 组合特征（L2归一化 + 标准化）
    print("  测试组合特征...")
    combined_features = scaler.fit_transform(normalized_features)
    result, config = optimized_knn_evaluation(combined_features, labels, is_original, "组合特征 ")
    if result:
        results['combined'] = {'result': result, 'config': config}
    
    # 找出最佳方法
    best_method = None
    best_individual_acc = 0.0
    
    for method, data in results.items():
        individual_acc = data['result']['individual_acc']
        if individual_acc > best_individual_acc:
            best_individual_acc = individual_acc
            best_method = method
    
    if best_method:
        print(f"\n🏆 最佳特征增强方法: {best_method}")
        print(f"  个体猫咪准确率: {results[best_method]['result']['individual_acc']*100:.2f}%")
        print(f"  最佳配置: {results[best_method]['config']}")
    
    return results

def comprehensive_model_evaluation(model_paths: List[str]):
    """综合模型评估"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 准备测试数据
    print("📁 准备测试数据...")
    
    with open('../tagging/annotations.json', 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建测试数据集
    test_dataset = EnhancedProgressiveDataset(
        annotations, '../dataset/cat_individual_images', '../dataset/renamed_thumbnails', 
        cat_to_id, max_individual_cats=30, is_training=False
    )
    
    # 使用更大的测试集
    test_indices = random.sample(range(len(test_dataset)), min(800, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=16, shuffle=False, num_workers=2)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    all_results = {}
    
    for model_path in model_paths:
        if not os.path.exists(model_path):
            print(f"⏭️  跳过不存在的模型: {model_path}")
            continue
        
        print(f"\n{'='*60}")
        print(f"🔍 评估模型: {os.path.basename(model_path)}")
        print(f"{'='*60}")
        
        try:
            # 加载模型
            model, checkpoint = load_model_safely(model_path, device)
            
            # 提取特征
            print("🔍 提取特征...")
            features, labels, is_original, categories = extract_features(model, test_loader, device)
            
            print(f"  特征维度: {features.shape}")
            print(f"  样本数量: {len(labels)}")
            print(f"  类别数量: {len(set(labels))}")
            
            # 特征增强评估
            results = feature_enhancement_evaluation(features, labels, is_original)
            
            all_results[os.path.basename(model_path)] = results
            
            # 清理内存
            del model
            torch.cuda.empty_cache()
            
        except Exception as e:
            print(f"❌ 评估模型失败: {e}")
            continue
    
    # 生成最终报告
    print(f"\n{'='*80}")
    print("🏆 最终综合评估报告")
    print(f"{'='*80}")
    
    best_overall_result = None
    best_overall_acc = 0.0
    
    for model_name, results in all_results.items():
        print(f"\n📊 {model_name}:")
        
        for method, data in results.items():
            individual_acc = data['result']['individual_acc']
            print(f"  {method}: {individual_acc*100:.2f}%")
            
            if individual_acc > best_overall_acc:
                best_overall_acc = individual_acc
                best_overall_result = {
                    'model': model_name,
                    'method': method,
                    'result': data['result'],
                    'config': data['config']
                }
    
    if best_overall_result:
        print(f"\n🎯 全局最佳结果:")
        print(f"  模型: {best_overall_result['model']}")
        print(f"  方法: {best_overall_result['method']}")
        print(f"  个体准确率: {best_overall_result['result']['individual_acc']*100:.2f}%")
        print(f"  原始准确率: {best_overall_result['result']['original_acc']*100:.2f}%")
        print(f"  总体准确率: {best_overall_result['result']['overall_acc']*100:.2f}%")
        print(f"  最佳配置: {best_overall_result['config']}")
    
    # 保存结果
    output_file = "optimized_knn_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📄 详细结果已保存到: {output_file}")
    
    return all_results

def main():
    parser = argparse.ArgumentParser(description='优化的KNN评估')
    parser.add_argument('--models', nargs='+', default=['best_contrastive_model.pth'], help='模型文件路径列表')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    print("🚀 开始优化的KNN评估...")
    results = comprehensive_model_evaluation(args.models)
    print("✅ 优化的KNN评估完成！")

if __name__ == "__main__":
    main()
