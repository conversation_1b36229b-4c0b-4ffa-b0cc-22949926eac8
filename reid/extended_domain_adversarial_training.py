#!/usr/bin/env python3
"""
扩展的域对抗训练 - 支持cat_individual_images数据集的泛化能力提升
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image, ImageEnhance
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))
sys.path.append(str(Path(__file__).parent / "infrared_cat_recognition_project" / "training"))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import GradientReversalLayer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExtendedDomainClassifier(nn.Module):
    """扩展的域分类器 - 支持多个域"""
    
    def __init__(self, feature_dim=2048, num_domains=3):
        super(ExtendedDomainClassifier, self).__init__()
        self.classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, feature_dim // 4),
            nn.BatchNorm1d(feature_dim // 4),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 4, num_domains)  # 支持多个域
        )
    
    def forward(self, x, alpha=1.0):
        # 应用梯度反转
        reversed_features = GradientReversalLayer.apply(x, alpha)
        domain_pred = self.classifier(reversed_features)
        return domain_pred

class ExtendedDomainAdaptationModel(nn.Module):
    """扩展的域适应模型 - 支持任意数量的猫咪分类"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=513, num_domains=3):
        super(ExtendedDomainAdaptationModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        self.num_domains = num_domains
        
        # 增强的特征提取层
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 扩展的猫咪分类器
        self.cat_classifier = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(feature_dim // 2, num_classes)
        )
        
        # 扩展的域分类器
        self.domain_classifier = ExtendedDomainClassifier(feature_dim, num_domains)
    
    def forward(self, x, alpha=1.0, return_features=False):
        # 特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.feature_enhancer(base_features)
        
        if return_features:
            return enhanced_features
        
        # 猫咪分类
        cat_logits = self.cat_classifier(enhanced_features)
        
        # 域分类（带梯度反转）
        domain_logits = self.domain_classifier(enhanced_features, alpha)
        
        return cat_logits, domain_logits, enhanced_features

class MultiDomainDataset(Dataset):
    """多域数据集 - 整合原始图像、红外图像和cat_individual_images"""
    
    def __init__(self, original_annotations: Dict, infrared_annotations: Dict,
                 individual_data_dir: str, original_dir: str, infrared_dir: str,
                 cat_to_id: Dict[str, int], max_individual_cats: int = 50,
                 is_training=True):
        
        self.original_dir = original_dir
        self.infrared_dir = infrared_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        self.samples = []
        
        # 处理原始图像数据 (域0)
        for filename, annotation in original_annotations.items():
            if annotation['category'] in cat_to_id:
                self.samples.append({
                    'image_path': os.path.join(original_dir, filename),
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'domain': 0  # 原始图像域
                })
        
        # 处理红外图像数据 (域1)
        for filename, annotation in infrared_annotations.items():
            if annotation['category'] in cat_to_id:
                self.samples.append({
                    'image_path': os.path.join(infrared_dir, filename),
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'domain': 1  # 红外图像域
                })
        
        # 处理cat_individual_images数据 (域2)
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            # 限制个体猫咪数量
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                # 为新个体分配ID
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        self.samples.append({
                            'image_path': os.path.join(folder_path, img_file),
                            'category': cat_name,
                            'label': cat_to_id[cat_name],
                            'domain': 2  # 个体图像域
                        })
        
        # 平衡不同域的样本数量
        domain_samples = defaultdict(list)
        for sample in self.samples:
            domain_samples[sample['domain']].append(sample)
        
        # 记录统计信息
        logger.info(f"域0 (原始图像): {len(domain_samples[0])} 样本")
        logger.info(f"域1 (红外图像): {len(domain_samples[1])} 样本")
        logger.info(f"域2 (个体图像): {len(domain_samples[2])} 样本")
        logger.info(f"总类别数: {len(cat_to_id)}")
        
        # 随机打乱样本
        random.shuffle(self.samples)
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        try:
            image = Image.open(sample['image_path']).convert('RGB')
            
            # 对红外图像进行特殊处理
            if sample['domain'] == 1 and self.is_training and random.random() < 0.3:
                enhancer = ImageEnhance.Contrast(image)
                image = enhancer.enhance(random.uniform(1.1, 1.5))
                
        except Exception as e:
            logger.error(f"无法加载图像 {sample['image_path']}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        image = self.transform(image)
        return image, sample['label'], sample['domain'], sample['category']

def create_extended_datasets(original_annotations_file: str, infrared_annotations_file: str,
                           individual_data_dir: str, original_dir: str, infrared_dir: str,
                           max_individual_cats: int = 50, train_ratio: float = 0.8):
    """创建扩展的多域数据集"""
    
    # 加载标注数据
    with open(original_annotations_file, 'r', encoding='utf-8') as f:
        original_annotations = json.load(f)
    
    with open(infrared_annotations_file, 'r', encoding='utf-8') as f:
        infrared_annotations = json.load(f)
    
    # 创建类别映射
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建完整数据集以获取所有类别
    full_dataset = MultiDomainDataset(
        original_annotations, infrared_annotations, individual_data_dir,
        original_dir, infrared_dir, cat_to_id, max_individual_cats, True
    )
    
    # 更新cat_to_id
    cat_to_id = full_dataset.cat_to_id
    id_to_cat = {v: k for k, v in cat_to_id.items()}
    
    # 分割训练和验证数据
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * train_ratio))
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    # 创建训练和验证数据集
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    return train_dataset, val_dataset, cat_to_id, id_to_cat

def train_extended_domain_model(model, train_loader, val_loader, device, epochs, lr):
    """训练扩展的域适应模型"""

    # 优化器设置
    optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=epochs)

    # 损失函数
    cat_criterion = nn.CrossEntropyLoss()
    domain_criterion = nn.CrossEntropyLoss()

    best_val_acc = 0.0
    patience = 10
    patience_counter = 0

    for epoch in range(epochs):
        model.train()

        # 动态调整alpha值
        p = float(epoch) / epochs
        alpha = 2. / (1. + np.exp(-10 * p)) - 1

        total_loss = 0.0
        cat_loss_sum = 0.0
        domain_loss_sum = 0.0
        num_batches = 0

        for images, cat_labels, domain_labels, _ in train_loader:
            images = images.to(device)
            cat_labels = cat_labels.to(device)
            domain_labels = domain_labels.to(device)

            optimizer.zero_grad()

            # 前向传播
            cat_logits, domain_logits, features = model(images, alpha)

            # 计算损失
            cat_loss = cat_criterion(cat_logits, cat_labels)
            domain_loss = domain_criterion(domain_logits, domain_labels)

            # 对于原有的3猫类别，给予更高权重以保持性能
            original_cat_mask = cat_labels < 3
            if original_cat_mask.sum() > 0:
                original_cat_loss = cat_criterion(cat_logits[original_cat_mask], cat_labels[original_cat_mask])
                cat_loss = 0.7 * original_cat_loss + 0.3 * cat_loss

            # 总损失
            loss = cat_loss + 0.5 * domain_loss

            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            total_loss += loss.item()
            cat_loss_sum += cat_loss.item()
            domain_loss_sum += domain_loss.item()
            num_batches += 1

        scheduler.step()

        # 验证
        val_acc = evaluate_extended_model(model, val_loader, device)

        avg_loss = total_loss / num_batches
        avg_cat_loss = cat_loss_sum / num_batches
        avg_domain_loss = domain_loss_sum / num_batches

        logger.info(f"Epoch {epoch+1}/{epochs}")
        logger.info(f"  训练损失: {avg_loss:.4f} (猫咪: {avg_cat_loss:.4f}, 域: {avg_domain_loss:.4f})")
        logger.info(f"  验证准确率: {val_acc:.4f}")
        logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
        logger.info(f"  Alpha: {alpha:.4f}")

        # 早停机制
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'feature_dim': model.feature_dim,
                'num_classes': model.num_classes,
                'num_domains': model.num_domains,
                'epoch': epoch,
                'val_acc': val_acc
            }, 'best_extended_domain_model.pth')
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"早停：验证准确率连续{patience}轮未提升")
                break

    logger.info(f"训练完成！最佳验证准确率: {best_val_acc:.4f}")
    return model

def evaluate_extended_model(model, val_loader, device):
    """评估扩展模型"""

    model.eval()

    # 提取特征用于KNN分类
    all_features = []
    all_labels = []
    all_domains = []

    with torch.no_grad():
        for images, cat_labels, domain_labels, _ in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)

            all_features.append(features.cpu().numpy())
            all_labels.extend(cat_labels.numpy())
            all_domains.extend(domain_labels.numpy())

    if len(all_features) == 0:
        return 0.0

    all_features = np.vstack(all_features)

    # 分别评估不同域的性能
    domain_accuracies = {}

    for domain_id in set(all_domains):
        domain_mask = np.array(all_domains) == domain_id
        if domain_mask.sum() < 2:
            continue

        domain_features = all_features[domain_mask]
        domain_labels = np.array(all_labels)[domain_mask]

        # 使用KNN分类
        if len(set(domain_labels)) > 1:
            knn = KNeighborsClassifier(n_neighbors=min(5, len(set(domain_labels))), metric='cosine')

            # 简单的留一法交叉验证
            predictions = []
            for i in range(len(domain_features)):
                train_mask = np.ones(len(domain_features), dtype=bool)
                train_mask[i] = False

                if train_mask.sum() > 0:
                    knn.fit(domain_features[train_mask], domain_labels[train_mask])
                    pred = knn.predict(domain_features[i:i+1])
                    predictions.append(pred[0])
                else:
                    predictions.append(domain_labels[i])

            accuracy = accuracy_score(domain_labels, predictions)
            domain_accuracies[domain_id] = accuracy

    # 计算总体准确率
    if domain_accuracies:
        overall_accuracy = np.mean(list(domain_accuracies.values()))

        # 记录各域性能
        domain_names = {0: "原始图像", 1: "红外图像", 2: "个体图像"}
        for domain_id, acc in domain_accuracies.items():
            domain_name = domain_names.get(domain_id, f"域{domain_id}")
            logger.info(f"    {domain_name}: {acc:.4f}")

        return overall_accuracy

    return 0.0

def main():
    parser = argparse.ArgumentParser(description='扩展的域对抗训练')
    parser.add_argument('--original-annotations', required=True, help='原始图像标注文件')
    parser.add_argument('--infrared-annotations', required=True, help='红外图像标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='cat_individual_images数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--infrared-dir', required=True, help='红外图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型路径')
    parser.add_argument('--output', default='extended_domain_model.pth', help='输出模型路径')
    parser.add_argument('--max-individual-cats', type=int, default=20, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=30, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')

    args = parser.parse_args()

    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")

    # 创建扩展数据集
    train_dataset, val_dataset, cat_to_id, id_to_cat = create_extended_datasets(
        args.original_annotations, args.infrared_annotations, args.individual_data_dir,
        args.original_dir, args.infrared_dir, args.max_individual_cats
    )

    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"总类别数: {len(cat_to_id)}")

    # 数据加载器
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)

    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)

    # 检查是否是域适应模型
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        # 这是域适应模型，需要提取基础特征提取器
        logger.info("检测到域适应模型，提取基础特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)

        # 提取基础特征提取器的权重
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value

        base_model.load_state_dict(base_state_dict)
    else:
        # 这是基础特征提取器模型
        logger.info("检测到基础特征提取器模型...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])

    # 创建扩展的域适应模型
    model = ExtendedDomainAdaptationModel(
        base_model, feature_dim, len(cat_to_id), num_domains=3
    )
    model = model.to(device)

    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 开始训练
    logger.info("开始扩展的域对抗训练...")
    model = train_extended_domain_model(model, train_loader, val_loader, device, args.epochs, args.lr)

    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'num_domains': 3,
        'cat_to_id': cat_to_id,
        'id_to_cat': id_to_cat
    }, args.output)

    logger.info(f"模型已保存到: {args.output}")
    logger.info("扩展的域对抗训练完成！")

if __name__ == "__main__":
    main()
