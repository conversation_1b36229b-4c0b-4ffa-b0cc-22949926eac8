#!/usr/bin/env python3
"""
测试对比学习模型的性能
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import time

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from contrastive_learning_enhancement import ContrastiveProgressiveModel
from enhanced_progressive_training import EnhancedProgressiveDataset

def load_contrastive_model(model_path: str, device: torch.device):
    """加载对比学习模型"""
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 33)
    
    # 创建基础特征提取器
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    
    # 创建对比学习模型
    model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    return model, checkpoint

def test_contrastive_model(model, test_loader, device):
    """测试对比学习模型"""
    
    print("🔍 开始测试对比学习模型...")
    
    all_features = []
    all_labels = []
    all_is_original = []
    all_categories = []
    inference_times = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in test_loader:
            start_time = time.time()
            
            images = images.to(device)
            features = model(images, return_features=True)
            
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
            all_categories.extend(categories)
    
    if len(all_features) == 0:
        print("❌ 没有有效的测试数据")
        return None
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # 性能指标
    avg_inference_time = np.mean(inference_times)
    throughput = len(all_labels) / sum(inference_times)
    
    print(f"📊 推理性能:")
    print(f"  平均推理时间: {avg_inference_time*1000:.2f} ms/batch")
    print(f"  吞吐量: {throughput:.1f} samples/sec")
    
    # 使用多种KNN配置进行测试
    knn_configs = [
        {'n_neighbors': 5, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 7, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 9, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 11, 'metric': 'cosine', 'weights': 'distance'},
    ]
    
    best_result = None
    best_individual_acc = 0.0
    
    print(f"\n🧪 测试不同KNN配置:")
    
    for i, config in enumerate(knn_configs):
        knn = KNeighborsClassifier(**config)
        
        # 留一法交叉验证
        predictions = []
        for j in range(len(all_features)):
            train_mask = np.ones(len(all_features), dtype=bool)
            train_mask[j] = False
            
            if train_mask.sum() > 0:
                knn.fit(all_features[train_mask], all_labels[train_mask])
                pred = knn.predict(all_features[j:j+1])
                predictions.append(pred[0])
            else:
                predictions.append(all_labels[j])
        
        predictions = np.array(predictions)
        
        # 计算准确率
        overall_acc = accuracy_score(all_labels, predictions)
        
        original_mask = all_is_original.astype(bool)
        original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
        
        individual_mask = ~original_mask
        individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
        
        print(f"  配置 {i+1} (k={config['n_neighbors']}): 总体={overall_acc:.4f}, 原始={original_acc:.4f}, 个体={individual_acc:.4f}")
        
        # 选择个体准确率最高的配置
        if individual_acc > best_individual_acc:
            best_individual_acc = individual_acc
            best_result = {
                'overall_acc': overall_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc,
                'config': config
            }
    
    if best_result is None:
        print("❌ 测试失败")
        return None
    
    print(f"\n🏆 最佳结果 (k={best_result['config']['n_neighbors']}):")
    print(f"  总体准确率: {best_result['overall_acc']:.4f} ({best_result['overall_acc']*100:.2f}%)")
    print(f"  原始3猫准确率: {best_result['original_acc']:.4f} ({best_result['original_acc']*100:.2f}%)")
    print(f"  个体猫咪准确率: {best_result['individual_acc']:.4f} ({best_result['individual_acc']*100:.2f}%)")
    
    # 性能评级
    if best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.75:
        grade = "🏆 卓越"
    elif best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.65:
        grade = "🥇 优秀"
    elif best_result['original_acc'] >= 0.90 and best_result['individual_acc'] >= 0.50:
        grade = "🥈 良好"
    elif best_result['original_acc'] >= 0.85 and best_result['individual_acc'] >= 0.35:
        grade = "🥉 一般"
    else:
        grade = "❌ 需改进"
    
    print(f"  综合评级: {grade}")
    
    # 计算提升倍数
    baseline_individual_acc = 0.1858  # 基线18.58%
    improvement_ratio = best_result['individual_acc'] / baseline_individual_acc
    print(f"  相比基线提升: {improvement_ratio:.1f}x ({(improvement_ratio-1)*100:.1f}%)")
    
    # 详细分析
    print(f"\n📋 详细分析:")
    print(f"  测试样本总数: {len(all_labels)}")
    print(f"  原始3猫样本: {original_mask.sum()}")
    print(f"  个体猫咪样本: {individual_mask.sum()}")
    print(f"  类别总数: {len(set(all_labels))}")
    
    # 错误分析
    errors = predictions != all_labels
    error_count = errors.sum()
    print(f"  错误预测数: {error_count} / {len(all_labels)} ({error_count/len(all_labels)*100:.2f}%)")
    
    if error_count > 0:
        original_errors = errors[original_mask].sum() if original_mask.sum() > 0 else 0
        individual_errors = errors[individual_mask].sum() if individual_mask.sum() > 0 else 0
        print(f"    原始3猫错误: {original_errors}")
        print(f"    个体猫咪错误: {individual_errors}")
    
    return best_result

def main():
    parser = argparse.ArgumentParser(description='测试对比学习模型')
    parser.add_argument('--model', required=True, help='模型文件路径')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--max-cats', type=int, default=30, help='最大猫咪数量')
    parser.add_argument('--test-samples', type=int, default=500, help='测试样本数量')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 加载模型
    print(f"📦 加载对比学习模型: {args.model}")
    model, checkpoint = load_contrastive_model(args.model, device)
    
    print(f"✅ 模型加载成功")
    print(f"  特征维度: {checkpoint.get('feature_dim', 2048)}")
    print(f"  类别数量: {checkpoint.get('num_classes', 33)}")
    print(f"  训练轮数: {checkpoint.get('epoch', 'Unknown')}")
    
    # 准备测试数据
    print(f"📁 准备测试数据...")
    
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建测试数据集
    test_dataset = EnhancedProgressiveDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_cats, is_training=False
    )
    
    # 随机采样测试数据
    test_indices = random.sample(range(len(test_dataset)), min(args.test_samples, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    # 测试模型
    result = test_contrastive_model(model, test_loader, device)
    
    if result:
        # 保存结果
        output_file = "contrastive_model_test_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📄 详细结果已保存到: {output_file}")
        print("✅ 对比学习模型测试完成！")
    else:
        print("❌ 测试失败")

if __name__ == "__main__":
    main()
