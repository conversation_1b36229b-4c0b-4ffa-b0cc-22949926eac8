#!/usr/bin/env python3
"""
测试渐进式模型的性能
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel, ProgressiveDataset

def load_progressive_model(model_path: str, device: torch.device):
    """加载渐进式模型"""
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    
    # 获取模型参数
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 23)
    
    # 创建基础特征提取器
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    
    # 创建渐进式模型
    model = ProgressiveModel(base_model, feature_dim, num_classes)
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)
    model.eval()
    
    return model

def comprehensive_test(model_path: str, annotations_file: str, individual_data_dir: str, 
                      original_dir: str, max_cats: int = 50):
    """全面测试模型性能"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载模型
    print("加载渐进式模型...")
    model = load_progressive_model(model_path, device)
    
    # 加载标注数据
    with open(annotations_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 创建类别映射
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建测试数据集
    print(f"创建测试数据集 (最多 {max_cats} 只猫咪)...")
    test_dataset = ProgressiveDataset(
        annotations, individual_data_dir, original_dir, 
        cat_to_id, max_cats, is_training=False
    )
    
    # 分离原始3猫和个体猫咪数据
    original_samples = []
    individual_samples = []
    
    for i, sample in enumerate(test_dataset.samples):
        if sample['is_original']:
            original_samples.append(i)
        else:
            individual_samples.append(i)
    
    print(f"原始3猫样本: {len(original_samples)}")
    print(f"个体猫咪样本: {len(individual_samples)}")
    
    # 测试原始3猫性能
    print("\n=== 测试原始3猫性能 ===")
    if original_samples:
        original_subset = torch.utils.data.Subset(test_dataset, original_samples[:300])  # 随机取300个样本
        original_loader = DataLoader(original_subset, batch_size=16, shuffle=True, num_workers=2)
        original_acc = test_subset(model, original_loader, device, "原始3猫")
        print(f"原始3猫准确率: {original_acc:.4f} ({original_acc*100:.2f}%)")
    
    # 测试个体猫咪性能
    print("\n=== 测试个体猫咪性能 ===")
    if individual_samples:
        individual_subset = torch.utils.data.Subset(test_dataset, individual_samples[:200])  # 随机取200个样本
        individual_loader = DataLoader(individual_subset, batch_size=16, shuffle=True, num_workers=2)
        individual_acc = test_subset(model, individual_loader, device, "个体猫咪")
        print(f"个体猫咪准确率: {individual_acc:.4f} ({individual_acc*100:.2f}%)")
    
    # 混合测试
    print("\n=== 混合测试 ===")
    mixed_samples = original_samples[:150] + individual_samples[:150]
    random.shuffle(mixed_samples)
    mixed_subset = torch.utils.data.Subset(test_dataset, mixed_samples)
    mixed_loader = DataLoader(mixed_subset, batch_size=16, shuffle=True, num_workers=2)
    mixed_acc = test_subset(model, mixed_loader, device, "混合数据")
    print(f"混合数据准确率: {mixed_acc:.4f} ({mixed_acc*100:.2f}%)")
    
    # 性能分析
    print("\n=== 性能分析 ===")
    if 'original_acc' in locals() and 'individual_acc' in locals():
        if original_acc >= 0.95:
            print("✅ 原始3猫性能优秀 (≥95%)")
        elif original_acc >= 0.90:
            print("🔶 原始3猫性能良好 (≥90%)")
        else:
            print("⚠️  原始3猫性能需要改进 (<90%)")
        
        if individual_acc >= 0.80:
            print("✅ 个体猫咪泛化能力优秀 (≥80%)")
        elif individual_acc >= 0.60:
            print("🔶 个体猫咪泛化能力良好 (≥60%)")
        elif individual_acc >= 0.40:
            print("⚠️  个体猫咪泛化能力一般 (≥40%)")
        else:
            print("❌ 个体猫咪泛化能力较差 (<40%)")
        
        improvement = individual_acc / 0.1858  # 与之前18.58%的基线比较
        print(f"相比基线提升: {improvement:.1f}x ({individual_acc/0.1858*100-100:.1f}%)")
    
    return {
        'original_acc': original_acc if 'original_acc' in locals() else 0,
        'individual_acc': individual_acc if 'individual_acc' in locals() else 0,
        'mixed_acc': mixed_acc if 'mixed_acc' in locals() else 0
    }

def test_subset(model, dataloader, device, subset_name):
    """测试数据子集"""
    
    all_features = []
    all_labels = []
    all_categories = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in dataloader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_categories.extend(categories)
    
    if len(all_features) == 0:
        return 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    
    # 使用KNN分类
    unique_labels = list(set(all_labels))
    if len(unique_labels) < 2:
        return 1.0 if len(unique_labels) == 1 else 0.0
    
    knn = KNeighborsClassifier(n_neighbors=min(7, len(unique_labels)), metric='cosine')
    
    # 留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    accuracy = accuracy_score(all_labels, predictions)
    
    # 显示详细信息
    if len(unique_labels) <= 10:  # 只有类别数较少时才显示详细报告
        category_names = list(set(all_categories))
        if len(category_names) <= 10:
            print(f"\n{subset_name} 详细分类报告:")
            report = classification_report(all_labels, predictions, 
                                         target_names=[f"ID_{i}" for i in unique_labels],
                                         labels=unique_labels, zero_division=0)
            print(report)
    
    return accuracy

def main():
    parser = argparse.ArgumentParser(description='测试渐进式模型性能')
    parser.add_argument('--model', required=True, help='渐进式模型路径')
    parser.add_argument('--annotations', required=True, help='标注文件路径')
    parser.add_argument('--individual-data-dir', required=True, help='个体猫咪数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--max-cats', type=int, default=50, help='最大猫咪数量')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 全面测试
    results = comprehensive_test(args.model, args.annotations, args.individual_data_dir, 
                               args.original_dir, args.max_cats)
    
    # 保存结果
    output_file = "progressive_model_test_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({
            'original_accuracy': results['original_acc'],
            'individual_accuracy': results['individual_acc'],
            'mixed_accuracy': results['mixed_acc'],
            'model_path': args.model,
            'max_cats': args.max_cats,
            'test_timestamp': str(torch.utils.data.get_worker_info())
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n测试结果已保存到: {output_file}")
    
    # 总结
    print(f"\n=== 最终总结 ===")
    print(f"原始3猫准确率: {results['original_acc']:.4f} ({results['original_acc']*100:.2f}%)")
    print(f"个体猫咪准确率: {results['individual_acc']:.4f} ({results['individual_acc']*100:.2f}%)")
    print(f"混合数据准确率: {results['mixed_acc']:.4f} ({results['mixed_acc']*100:.2f}%)")
    
    if results['original_acc'] >= 0.95 and results['individual_acc'] >= 0.40:
        print("🎉 渐进式迁移学习成功！既保持了原有性能，又显著提升了泛化能力！")
    elif results['original_acc'] >= 0.90:
        print("👍 渐进式迁移学习基本成功，原有性能保持良好，泛化能力有所提升")
    else:
        print("⚠️  需要进一步优化训练策略")

if __name__ == "__main__":
    main()
