#!/usr/bin/env python3
"""
知识蒸馏增强 - 使用对比学习模型作为教师网络，训练更强的学生网络
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from contrastive_learning_enhancement import ContrastiveProgressiveModel
from enhanced_progressive_training import EnhancedProgressiveDataset

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StudentModel(nn.Module):
    """学生网络 - 更深更强的架构"""
    
    def __init__(self, base_model, feature_dim=2048, num_classes=33):
        super(StudentModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.num_classes = num_classes
        
        # 解冻更多层
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 解冻最后两个stage
        for param in self.feature_extractor.backbone.layers[-2:].parameters():
            param.requires_grad = True
        for param in self.feature_extractor.backbone.norm.parameters():
            param.requires_grad = True
        
        # 更深的特征增强网络
        self.feature_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 3),
            nn.BatchNorm1d(feature_dim * 3),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(feature_dim * 3, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # 多尺度特征融合
        self.multi_scale_fusion = nn.ModuleList([
            nn.Sequential(
                nn.Linear(feature_dim, feature_dim // 2),
                nn.ReLU(),
                nn.Dropout(0.2)
            ),
            nn.Sequential(
                nn.Linear(feature_dim, feature_dim // 4),
                nn.ReLU(),
                nn.Dropout(0.2)
            )
        ])
        
        # 融合后的特征维度
        fused_dim = feature_dim + feature_dim // 2 + feature_dim // 4
        
        # 最终分类器
        self.classifier = nn.Sequential(
            nn.Linear(fused_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            
            nn.Linear(feature_dim, feature_dim // 2),
            nn.BatchNorm1d(feature_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.4),
            
            nn.Linear(feature_dim // 2, num_classes)
        )
    
    def forward(self, x, return_features=False):
        # 基础特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.feature_enhancer(base_features)
        
        # 多尺度特征
        multi_scale_features = [enhanced_features]
        for fusion_layer in self.multi_scale_fusion:
            multi_scale_features.append(fusion_layer(enhanced_features))
        
        # 特征融合
        fused_features = torch.cat(multi_scale_features, dim=1)
        
        if return_features:
            return fused_features
        
        # 分类
        logits = self.classifier(fused_features)
        return logits, fused_features

class DistillationLoss(nn.Module):
    """知识蒸馏损失"""
    
    def __init__(self, temperature=4.0, alpha=0.7):
        super(DistillationLoss, self).__init__()
        self.temperature = temperature
        self.alpha = alpha
        self.kl_div = nn.KLDivLoss(reduction='batchmean')
        self.ce_loss = nn.CrossEntropyLoss()
    
    def forward(self, student_logits, teacher_logits, labels):
        # 软标签蒸馏损失
        soft_teacher = F.softmax(teacher_logits / self.temperature, dim=1)
        soft_student = F.log_softmax(student_logits / self.temperature, dim=1)
        distillation_loss = self.kl_div(soft_student, soft_teacher) * (self.temperature ** 2)
        
        # 硬标签分类损失
        classification_loss = self.ce_loss(student_logits, labels)
        
        # 组合损失
        total_loss = self.alpha * distillation_loss + (1 - self.alpha) * classification_loss
        
        return total_loss, distillation_loss, classification_loss

def load_teacher_model(model_path: str, device: torch.device):
    """加载教师模型"""
    
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', 33)
    
    # 创建基础特征提取器
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    
    # 创建对比学习模型
    teacher_model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
    teacher_model.load_state_dict(checkpoint['model_state_dict'])
    teacher_model = teacher_model.to(device)
    teacher_model.eval()
    
    # 冻结教师模型
    for param in teacher_model.parameters():
        param.requires_grad = False
    
    return teacher_model

def distillation_train_epoch(student_model, teacher_model, train_loader, optimizer, 
                           distillation_criterion, device, epoch, total_epochs):
    """知识蒸馏训练一个epoch"""
    
    student_model.train()
    teacher_model.eval()
    
    total_loss = 0.0
    distillation_loss_sum = 0.0
    classification_loss_sum = 0.0
    num_batches = 0
    
    for images, labels, categories, is_original in train_loader:
        images = images.to(device)
        labels = labels.to(device)
        is_original = is_original.to(device)
        
        optimizer.zero_grad()
        
        # 学生模型前向传播
        student_logits, student_features = student_model(images)
        
        # 教师模型前向传播
        with torch.no_grad():
            teacher_logits, teacher_features, _ = teacher_model(images)
        
        # 知识蒸馏损失
        total_loss_batch, distillation_loss, classification_loss = distillation_criterion(
            student_logits, teacher_logits, labels
        )
        
        # 对原始3猫数据给予更高权重
        weights = torch.where(is_original, 2.0, 1.0).mean()
        total_loss_batch = total_loss_batch * weights
        
        # 特征对齐损失 (学生特征向教师特征对齐)
        feature_alignment_loss = F.mse_loss(
            F.normalize(student_features, dim=1), 
            F.normalize(teacher_features, dim=1)
        )
        
        total_loss_batch += 0.1 * feature_alignment_loss
        
        total_loss_batch.backward()
        torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += total_loss_batch.item()
        distillation_loss_sum += distillation_loss.item()
        classification_loss_sum += classification_loss.item()
        num_batches += 1
    
    avg_total_loss = total_loss / num_batches
    avg_distillation_loss = distillation_loss_sum / num_batches
    avg_classification_loss = classification_loss_sum / num_batches
    
    logger.info(f"Epoch {epoch+1}/{total_epochs}")
    logger.info(f"  总损失: {avg_total_loss:.4f}")
    logger.info(f"  蒸馏损失: {avg_distillation_loss:.4f}")
    logger.info(f"  分类损失: {avg_classification_loss:.4f}")

def knowledge_distillation_training(student_model, teacher_model, train_loader, val_loader, 
                                  device, epochs, lr):
    """知识蒸馏训练"""
    
    # 分层学习率
    backbone_params = []
    other_params = []
    
    for name, param in student_model.named_parameters():
        if param.requires_grad:
            if 'feature_extractor.backbone' in name:
                backbone_params.append(param)
            else:
                other_params.append(param)
    
    optimizer = optim.AdamW([
        {'params': backbone_params, 'lr': lr * 0.1},
        {'params': other_params, 'lr': lr}
    ], weight_decay=1e-4)
    
    # 学习率调度器
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=15, T_mult=2)
    
    # 蒸馏损失函数
    distillation_criterion = DistillationLoss(temperature=5.0, alpha=0.8)
    
    best_val_acc = 0.0
    patience = 15
    patience_counter = 0
    
    for epoch in range(epochs):
        # 训练
        distillation_train_epoch(
            student_model, teacher_model, train_loader, optimizer,
            distillation_criterion, device, epoch, epochs
        )
        
        scheduler.step()
        
        # 验证
        val_acc, original_acc, individual_acc = evaluate_student_model(student_model, val_loader, device)
        
        logger.info(f"  验证结果 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
        logger.info(f"  学习率: backbone={scheduler.get_last_lr()[0]:.6f}, other={scheduler.get_last_lr()[1]:.6f}")
        
        # 早停和模型保存
        combined_score = 0.6 * original_acc + 0.4 * individual_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            
            # 保存最佳模型
            torch.save({
                'model_state_dict': student_model.state_dict(),
                'feature_dim': student_model.feature_dim,
                'num_classes': student_model.num_classes,
                'epoch': epoch,
                'val_acc': val_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc
            }, 'best_distilled_model.pth')
            
            logger.info(f"  ✅ 新的最佳学生模型 (综合得分: {combined_score:.4f})")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info(f"早停触发 (连续{patience}轮无改进)")
                break
    
    logger.info(f"知识蒸馏训练完成！最佳综合得分: {best_val_acc:.4f}")
    return student_model

def evaluate_student_model(model, val_loader, device):
    """评估学生模型"""
    
    model.eval()
    
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        return 0.0, 0.0, 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(9, len(set(all_labels))), 
                              metric='cosine', weights='distance')
    
    # 留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    # 计算准确率
    overall_acc = accuracy_score(all_labels, predictions)
    
    original_mask = all_is_original.astype(bool)
    original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
    
    individual_mask = ~original_mask
    individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
    
    return overall_acc, original_acc, individual_acc

def main():
    parser = argparse.ArgumentParser(description='知识蒸馏增强训练')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--teacher-model', required=True, help='教师模型路径')
    parser.add_argument('--output', default='distilled_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=30, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=35, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=12, help='批次大小')
    parser.add_argument('--lr', type=float, default=8e-5, help='学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载教师模型
    logger.info("加载教师模型...")
    teacher_model = load_teacher_model(args.teacher_model, device)
    
    # 数据准备
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    full_dataset = EnhancedProgressiveDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    # 数据分割
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * 0.85))
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    logger.info(f"总类别数: {len(cat_to_id)}")
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # 创建学生模型
    logger.info("创建学生模型...")
    checkpoint = torch.load(args.teacher_model, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    base_model = FeatureExtractorModel(feature_dim=feature_dim)
    student_model = StudentModel(base_model, feature_dim, len(cat_to_id))
    student_model = student_model.to(device)
    
    trainable_params = sum(p.numel() for p in student_model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in student_model.parameters())
    logger.info(f"学生模型可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 开始知识蒸馏训练
    logger.info("开始知识蒸馏训练...")
    student_model = knowledge_distillation_training(
        student_model, teacher_model, train_loader, val_loader, device, args.epochs, args.lr
    )
    
    # 保存最终模型
    torch.save({
        'model_state_dict': student_model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"知识蒸馏模型已保存到: {args.output}")

if __name__ == "__main__":
    main()
