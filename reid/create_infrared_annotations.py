#!/usr/bin/env python3
"""
创建红外图像标注文件
基于原始图像标注文件，为红外图像创建对应的标注
"""

import os
import json
import argparse

def create_infrared_annotations(original_annotations_file, infrared_dir, output_file):
    """创建红外图像标注文件"""
    
    # 加载原始标注
    with open(original_annotations_file, 'r', encoding='utf-8') as f:
        original_annotations = json.load(f)
    
    infrared_annotations = {}
    
    # 获取红外图像目录中的所有文件
    if not os.path.exists(infrared_dir):
        print(f"红外图像目录不存在: {infrared_dir}")
        return
    
    infrared_files = set(os.listdir(infrared_dir))
    
    # 为每个原始图像寻找对应的红外图像
    for original_filename, annotation in original_annotations.items():
        # 生成对应的红外图像文件名
        base_name = original_filename.replace('.jpg', '').replace('_hls', '')
        infrared_filename = f"{base_name}_hls_ir.jpg"
        
        # 检查红外图像是否存在
        if infrared_filename in infrared_files:
            infrared_annotations[infrared_filename] = annotation.copy()
    
    print(f"原始标注: {len(original_annotations)} 个")
    print(f"红外标注: {len(infrared_annotations)} 个")
    print(f"匹配率: {len(infrared_annotations)/len(original_annotations)*100:.1f}%")
    
    # 保存红外图像标注
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(infrared_annotations, f, indent=2, ensure_ascii=False)
    
    print(f"红外图像标注已保存到: {output_file}")
    
    # 显示一些统计信息
    category_counts = {}
    for annotation in infrared_annotations.values():
        category = annotation.get('category', 'unknown')
        category_counts[category] = category_counts.get(category, 0) + 1
    
    print("\n红外图像类别分布:")
    for category, count in sorted(category_counts.items()):
        print(f"  {category}: {count} 张")

def main():
    parser = argparse.ArgumentParser(description='创建红外图像标注文件')
    parser.add_argument('--original-annotations', 
                       default='../tagging/annotations.json',
                       help='原始图像标注文件路径')
    parser.add_argument('--infrared-dir', 
                       default='../dataset/ir_thumbnails',
                       help='红外图像目录路径')
    parser.add_argument('--output', 
                       default='infrared_annotations.json',
                       help='输出的红外图像标注文件路径')
    
    args = parser.parse_args()
    
    create_infrared_annotations(args.original_annotations, args.infrared_dir, args.output)

if __name__ == "__main__":
    main()
