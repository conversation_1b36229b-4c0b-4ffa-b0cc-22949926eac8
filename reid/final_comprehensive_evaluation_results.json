[{"model_name": "对比学习模型", "model_type": "contrastive", "overall_acc": 0.9575, "original_acc": 1.0, "individual_acc": 0.7301587301587301, "inference_time": 0.012959532737731934, "throughput": 1234.6124141818545, "grade": "🥇 优秀", "improvement_ratio": 3.929810173082509, "best_config": {"n_neighbors": 7, "metric": "cosine", "weights": "distance"}}, {"model_name": "增强渐进式模型", "model_type": "progressive", "overall_acc": 0.87875, "original_acc": 1.0, "individual_acc": 0.23015873015873015, "inference_time": 0.014018392562866211, "throughput": 1141.3576790810478, "grade": "❌ 需改进", "improvement_ratio": 1.238744511080356, "best_config": {"n_neighbors": 7, "metric": "cosine", "weights": "distance"}}, {"model_name": "基础渐进式模型", "model_type": "progressive", "overall_acc": 0.87625, "original_acc": 0.9985163204747775, "individual_acc": 0.2222222222222222, "inference_time": 0.025308475494384766, "throughput": 632.1992805749974, "grade": "❌ 需改进", "improvement_ratio": 1.196029183112068, "best_config": {"n_neighbors": 9, "metric": "cosine", "weights": "distance"}}]