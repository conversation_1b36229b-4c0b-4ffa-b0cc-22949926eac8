#!/usr/bin/env python3
"""
综合模型对比测试 - 比较所有优化方案的性能
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import time

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel, ProgressiveDataset

def load_model_safely(model_path: str, device: torch.device, model_type: str = "progressive"):
    """安全加载模型"""
    
    if not os.path.exists(model_path):
        print(f"⚠️  模型文件不存在: {model_path}")
        return None
    
    try:
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        feature_dim = checkpoint.get('feature_dim', 2048)
        num_classes = checkpoint.get('num_classes', 23)
        
        # 创建基础特征提取器
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        
        if model_type == "progressive":
            # 渐进式模型
            model = ProgressiveModel(base_model, feature_dim, num_classes)
            model.load_state_dict(checkpoint['model_state_dict'])
        elif model_type == "contrastive":
            # 对比学习模型
            from contrastive_learning_enhancement import ContrastiveProgressiveModel
            model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
            model.load_state_dict(checkpoint['model_state_dict'])
        elif model_type == "multimodal":
            # 多模态模型
            from multimodal_fusion_enhancement import MultiModalFusionModel
            model = MultiModalFusionModel(base_model, feature_dim, num_classes)
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            print(f"未知模型类型: {model_type}")
            return None
        
        model = model.to(device)
        model.eval()
        
        return model, checkpoint
        
    except Exception as e:
        print(f"⚠️  加载模型失败 {model_path}: {e}")
        return None

def test_model_performance(model, test_loader, device, model_name: str, model_type: str = "progressive"):
    """测试模型性能"""
    
    print(f"\n=== 测试 {model_name} ===")
    
    all_features = []
    all_labels = []
    all_is_original = []
    inference_times = []
    
    with torch.no_grad():
        for batch in test_loader:
            start_time = time.time()
            
            if model_type == "multimodal":
                # 多模态模型需要特殊处理
                original_images, infrared_images, labels, categories, is_original, has_infrared = batch
                original_images = original_images.to(device)
                infrared_images = infrared_images.to(device)
                has_infrared = has_infrared.to(device)
                
                features = model(original_images, infrared_images, has_infrared, return_features=True)
            else:
                # 渐进式和对比学习模型
                images, labels, categories, is_original = batch
                images = images.to(device)
                
                features = model(images, return_features=True)
            
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        print("❌ 没有有效的测试数据")
        return None
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # 性能指标
    avg_inference_time = np.mean(inference_times)
    throughput = len(all_labels) / sum(inference_times)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(set(all_labels))), 
                              metric='cosine', weights='distance')
    
    # 留一法交叉验证
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    # 计算准确率
    overall_acc = accuracy_score(all_labels, predictions)
    
    original_mask = all_is_original.astype(bool)
    original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
    
    individual_mask = ~original_mask
    individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
    
    # 输出结果
    print(f"📊 性能指标:")
    print(f"  总体准确率: {overall_acc:.4f} ({overall_acc*100:.2f}%)")
    print(f"  原始3猫准确率: {original_acc:.4f} ({original_acc*100:.2f}%)")
    print(f"  个体猫咪准确率: {individual_acc:.4f} ({individual_acc*100:.2f}%)")
    print(f"  平均推理时间: {avg_inference_time*1000:.2f} ms/batch")
    print(f"  吞吐量: {throughput:.1f} samples/sec")
    
    # 性能评级
    if original_acc >= 0.95 and individual_acc >= 0.50:
        grade = "🏆 优秀"
    elif original_acc >= 0.90 and individual_acc >= 0.40:
        grade = "🥈 良好"
    elif original_acc >= 0.85 and individual_acc >= 0.30:
        grade = "🥉 一般"
    else:
        grade = "❌ 需改进"
    
    print(f"  综合评级: {grade}")
    
    return {
        'model_name': model_name,
        'overall_acc': overall_acc,
        'original_acc': original_acc,
        'individual_acc': individual_acc,
        'inference_time': avg_inference_time,
        'throughput': throughput,
        'grade': grade
    }

def comprehensive_comparison():
    """综合对比所有模型"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 准备测试数据
    print("📁 准备测试数据...")
    
    with open('../tagging/annotations.json', 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建测试数据集
    test_dataset = ProgressiveDataset(
        annotations, '../dataset/cat_individual_images', '../dataset/renamed_thumbnails', 
        cat_to_id, max_individual_cats=30, is_training=False
    )
    
    # 随机采样测试数据
    test_indices = random.sample(range(len(test_dataset)), min(500, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=16, shuffle=False, num_workers=2)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    # 待测试的模型列表
    models_to_test = [
        {
            'name': '基础渐进式模型',
            'path': 'best_progressive_model.pth',
            'type': 'progressive'
        },
        {
            'name': '增强渐进式模型',
            'path': 'best_enhanced_progressive_model_stage3.pth',
            'type': 'progressive'
        },
        {
            'name': '大规模训练模型',
            'path': 'best_enhanced_progressive_model_stage3.pth',  # 大规模训练可能还在进行
            'type': 'progressive'
        },
        {
            'name': '对比学习模型',
            'path': 'best_contrastive_model.pth',
            'type': 'contrastive'
        },
        {
            'name': '多模态融合模型',
            'path': 'best_multimodal_fusion_model.pth',
            'type': 'multimodal'
        }
    ]
    
    results = []
    
    # 测试每个模型
    for model_info in models_to_test:
        model_result = load_model_safely(model_info['path'], device, model_info['type'])
        
        if model_result is None:
            print(f"⏭️  跳过 {model_info['name']} (模型不可用)")
            continue
        
        model, checkpoint = model_result
        
        # 特殊处理多模态模型的测试数据
        if model_info['type'] == 'multimodal':
            # 为多模态模型创建特殊的测试加载器
            from multimodal_fusion_enhancement import MultiModalDataset
            
            with open('infrared_annotations.json', 'r', encoding='utf-8') as f:
                infrared_annotations = json.load(f)
            
            multimodal_dataset = MultiModalDataset(
                annotations, infrared_annotations, '../dataset/cat_individual_images',
                '../dataset/renamed_thumbnails', '../dataset/ir_thumbnails',
                cat_to_id, max_individual_cats=30, is_training=False
            )
            
            multimodal_indices = random.sample(range(len(multimodal_dataset)), min(500, len(multimodal_dataset)))
            multimodal_subset = torch.utils.data.Subset(multimodal_dataset, multimodal_indices)
            current_test_loader = DataLoader(multimodal_subset, batch_size=16, shuffle=False, num_workers=2)
        else:
            current_test_loader = test_loader
        
        # 测试模型
        result = test_model_performance(model, current_test_loader, device, 
                                      model_info['name'], model_info['type'])
        
        if result:
            results.append(result)
        
        # 清理内存
        del model
        torch.cuda.empty_cache()
    
    # 生成对比报告
    print("\n" + "="*80)
    print("🏆 综合对比报告")
    print("="*80)
    
    if not results:
        print("❌ 没有可用的测试结果")
        return
    
    # 按个体猫咪准确率排序
    results.sort(key=lambda x: x['individual_acc'], reverse=True)
    
    print(f"{'模型名称':<20} {'总体准确率':<12} {'原始3猫':<12} {'个体猫咪':<12} {'吞吐量':<15} {'评级':<10}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['model_name']:<20} "
              f"{result['overall_acc']*100:>8.2f}%   "
              f"{result['original_acc']*100:>8.2f}%   "
              f"{result['individual_acc']*100:>8.2f}%   "
              f"{result['throughput']:>10.1f} s/s   "
              f"{result['grade']:<10}")
    
    # 最佳模型推荐
    best_model = results[0]
    print(f"\n🎯 推荐模型: {best_model['model_name']}")
    print(f"   个体猫咪准确率: {best_model['individual_acc']*100:.2f}%")
    print(f"   原始3猫准确率: {best_model['original_acc']*100:.2f}%")
    
    # 保存结果
    output_file = "model_comparison_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='综合模型对比测试')
    parser.add_argument('--test-samples', type=int, default=500, help='测试样本数量')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    print("🚀 开始综合模型对比测试...")
    comprehensive_comparison()
    print("✅ 对比测试完成！")

if __name__ == "__main__":
    main()
