#!/usr/bin/env python3
"""
测试时增强评估 - 使用TTA提升准确率
专门针对99.5%目标的最终评估
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
from torchvision import transforms
from PIL import Image

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from few_shot_meta_learning import PrototypicalNetwork, FewShotDataset, meta_evaluate
from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedTTAEvaluator:
    """高级测试时增强评估器"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
        # 定义多种TTA变换
        self.tta_transforms = [
            # 原始
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 水平翻转
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomHorizontalFlip(1.0),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 中心裁剪 - 更大尺寸
            transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.CenterCrop((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 轻微旋转
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.RandomRotation(3),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 亮度调整
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ColorJitter(brightness=0.05),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 对比度调整
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ColorJitter(contrast=0.05),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 饱和度调整
            transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ColorJitter(saturation=0.05),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            # 多尺度 - 小尺寸
            transforms.Compose([
                transforms.Resize((200, 200)),
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        ]
    
    def tta_predict_features(self, images):
        """使用TTA进行特征预测"""
        self.model.eval()
        
        all_features = []
        
        with torch.no_grad():
            for i, transform in enumerate(self.tta_transforms):
                try:
                    # 应用变换
                    if isinstance(images, torch.Tensor):
                        # 如果输入是tensor，先转换为PIL再应用变换
                        pil_images = [transforms.ToPILImage()(img.cpu()) for img in images]
                        transformed_images = torch.stack([transform(img) for img in pil_images])
                    else:
                        transformed_images = torch.stack([transform(img) for img in images])
                    
                    transformed_images = transformed_images.to(self.device)
                    
                    # 提取特征
                    features = self.model(transformed_images)
                    all_features.append(features)
                    
                except Exception as e:
                    logger.warning(f"TTA变换 {i} 失败: {e}")
                    continue
        
        if not all_features:
            logger.error("所有TTA变换都失败了")
            return None
        
        # 平均所有TTA结果
        avg_features = torch.stack(all_features).mean(dim=0)
        return avg_features
    
    def evaluate_with_tta(self, dataset, num_episodes=100):
        """使用TTA评估模型"""
        logger.info(f"开始TTA评估，使用 {len(self.tta_transforms)} 种变换，{num_episodes} 个episodes")
        
        total_accuracy = 0.0
        original_correct = 0
        individual_correct = 0
        original_total = 0
        individual_total = 0
        
        successful_episodes = 0
        
        for episode in range(num_episodes):
            try:
                # 采样episode
                support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                    n_way=min(8, len(dataset.classes)), k_shot=2, q_query=6
                )
                
                # TTA for support and query
                support_features = self.tta_predict_features(support_images)
                query_features = self.tta_predict_features(query_images)
                
                if support_features is None or query_features is None:
                    continue
                
                support_labels = support_labels.to(self.device)
                query_labels = query_labels.to(self.device)
                
                # 计算原型并分类
                prototypes, prototype_labels = self.model.compute_prototypes(support_features, support_labels)
                predictions, _ = self.model.classify_by_prototypes(query_features, prototypes, prototype_labels)
                
                # 统计准确率
                accuracy = (predictions == query_labels).float().mean()
                total_accuracy += accuracy.item()
                
                # 分别统计原始3猫和个体猫
                for j, label in enumerate(query_labels):
                    if label.item() < 3:  # 原始3猫
                        original_total += 1
                        if predictions[j] == label:
                            original_correct += 1
                    else:  # 个体猫
                        individual_total += 1
                        if predictions[j] == label:
                            individual_correct += 1
                
                successful_episodes += 1
                
                if (episode + 1) % 20 == 0:
                    current_acc = total_accuracy / successful_episodes if successful_episodes > 0 else 0
                    logger.info(f"  进度: {episode + 1}/{num_episodes}, 当前准确率: {current_acc:.4f}")
                
            except Exception as e:
                logger.warning(f"Episode {episode} 失败: {e}")
                continue
        
        if successful_episodes == 0:
            logger.error("没有成功的episodes")
            return 0.0, 0.0, 0.0
        
        avg_accuracy = total_accuracy / successful_episodes
        original_accuracy = original_correct / original_total if original_total > 0 else 0
        individual_accuracy = individual_correct / individual_total if individual_total > 0 else 0
        
        logger.info(f"TTA评估完成: {successful_episodes}/{num_episodes} 个成功episodes")
        
        return avg_accuracy, original_accuracy, individual_accuracy

def load_and_evaluate_model(model_path, dataset, device):
    """加载并评估单个模型"""
    logger.info(f"评估模型: {model_path}")
    
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return None
    
    try:
        # 加载模型
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        feature_dim = checkpoint.get('feature_dim', 2048)
        
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        model = PrototypicalNetwork(base_model, feature_dim=feature_dim)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.to(device)
        model.eval()
        
        logger.info(f"✅ 模型加载成功")
        
        # 标准评估
        logger.info("  标准评估...")
        std_acc, std_orig, std_indiv = meta_evaluate(model, dataset, device)
        
        # TTA评估
        logger.info("  TTA评估...")
        tta_evaluator = AdvancedTTAEvaluator(model, device)
        tta_acc, tta_orig, tta_indiv = tta_evaluator.evaluate_with_tta(dataset, num_episodes=50)
        
        results = {
            'model_path': model_path,
            'standard': {'total': std_acc, 'original': std_orig, 'individual': std_indiv},
            'tta': {'total': tta_acc, 'original': tta_orig, 'individual': tta_indiv},
            'improvement': tta_acc - std_acc
        }
        
        logger.info(f"  标准评估: 总体={std_acc:.4f}, 原始={std_orig:.4f}, 个体={std_indiv:.4f}")
        logger.info(f"  TTA评估:  总体={tta_acc:.4f}, 原始={tta_orig:.4f}, 个体={tta_indiv:.4f}")
        logger.info(f"  TTA提升:  {(tta_acc - std_acc)*100:.2f} 个百分点")
        
        return results
        
    except Exception as e:
        logger.error(f"模型评估失败: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description='TTA评估 - 追求99.5%准确率')
    parser.add_argument('--annotations', required=True, help='标注文件路径')
    parser.add_argument('--individual-data-dir', required=True, help='个体猫数据目录')
    parser.add_argument('--original-dir', required=True, help='原始猫数据目录')
    parser.add_argument('--models', nargs='+', required=True, help='模型文件路径列表')
    
    args = parser.parse_args()
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载数据
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 创建cat_to_id映射
    original_cats = {'小白': 0, '小花': 1, '小黑': 2}
    cat_to_id = original_cats.copy()
    
    individual_dirs = [d for d in os.listdir(args.individual_data_dir) 
                      if os.path.isdir(os.path.join(args.individual_data_dir, d))]
    for i, cat_dir in enumerate(sorted(individual_dirs)):
        if cat_dir not in cat_to_id:
            cat_to_id[cat_dir] = len(cat_to_id)
    
    # 创建数据集
    dataset = FewShotDataset(annotations, args.individual_data_dir, 
                           args.original_dir, cat_to_id, max_individual_cats=50)
    
    logger.info(f"数据集: {len(cat_to_id)} 个类别")
    
    # 评估所有模型
    all_results = []
    
    for model_path in args.models:
        result = load_and_evaluate_model(model_path, dataset, device)
        if result:
            all_results.append(result)
    
    # 总结结果
    logger.info("\n" + "="*80)
    logger.info("🏆 最终评估结果总结")
    logger.info("="*80)
    
    best_tta_acc = 0.0
    best_model = None
    
    for result in all_results:
        model_name = os.path.basename(result['model_path'])
        tta_acc = result['tta']['total']
        tta_orig = result['tta']['original']
        tta_indiv = result['tta']['individual']
        improvement = result['improvement']
        
        logger.info(f"\n📊 {model_name}:")
        logger.info(f"   TTA总体准确率: {tta_acc:.4f} ({tta_acc*100:.2f}%)")
        logger.info(f"   TTA原始3猫:   {tta_orig:.4f} ({tta_orig*100:.2f}%)")
        logger.info(f"   TTA个体猫:    {tta_indiv:.4f} ({tta_indiv*100:.2f}%)")
        logger.info(f"   TTA提升:      {improvement*100:.2f} 个百分点")
        
        if tta_acc > best_tta_acc:
            best_tta_acc = tta_acc
            best_model = result
    
    # 最佳结果
    if best_model:
        logger.info(f"\n🥇 最佳模型: {os.path.basename(best_model['model_path'])}")
        logger.info(f"   最佳TTA准确率: {best_tta_acc:.4f} ({best_tta_acc*100:.2f}%)")
        
        # 检查是否达到目标
        target_accuracy = 0.995
        if best_tta_acc >= target_accuracy:
            logger.info(f"🎉🎉🎉 成功达到目标准确率 {target_accuracy:.1%}!")
        else:
            gap = (target_accuracy - best_tta_acc) * 100
            logger.info(f"📈 距离目标还差 {gap:.2f} 个百分点")
            
            if gap < 0.5:
                logger.info("💡 建议: 非常接近目标！可以尝试更多TTA变换或更大的评估样本")
            elif gap < 1.0:
                logger.info("💡 建议: 很接近目标，可以尝试模型集成或更精细的TTA策略")
            elif gap < 2.0:
                logger.info("💡 建议: 可以尝试更强的数据增强或模型架构改进")
            else:
                logger.info("💡 建议: 需要更根本的改进，如更好的特征提取或数据质量提升")

if __name__ == "__main__":
    main()
