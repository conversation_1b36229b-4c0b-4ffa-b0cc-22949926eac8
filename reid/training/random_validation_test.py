#!/usr/bin/env python3
"""
随机验证测试脚本
随机抽取图片进行多轮测试，验证模型性能的真实性和稳定性
"""

import os
import sys
import json
import argparse
import logging
import random
from pathlib import Path
from typing import Dict, List, Tuple
import statistics

import torch
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score, confusion_matrix
from sklearn.neighbors import KNeighborsClassifier
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from feature_based_cat_recognition import FeatureExtractorModel
from domain_adversarial_training import DomainAdaptationModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RandomValidationTester:
    """随机验证测试器"""
    
    def __init__(self, model_path: str, device: str = "auto"):
        self.model_path = model_path
        self.device = torch.device('cuda' if torch.cuda.is_available() and device == "auto" else device)
        self.model = None
        self.cat_to_id = None
        self.id_to_cat = None
        self.scaler = StandardScaler()
        
        # 图像变换
        self.transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        self.load_model()
    
    def load_model(self):
        """加载域适应模型"""
        logger.info(f"加载域适应模型: {self.model_path}")
        
        checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
        
        # 获取模型配置
        feature_dim = checkpoint.get('feature_dim', 2048)
        self.cat_to_id = checkpoint.get('cat_to_id', {'小白': 0, '小花': 1, '小黑': 2})
        self.id_to_cat = checkpoint.get('id_to_cat', {0: '小白', 1: '小花', 2: '小黑'})
        
        # 创建基础模型
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['base_model_state_dict'])
        
        # 创建域适应模型
        self.model = DomainAdaptationModel(base_model, feature_dim, len(self.cat_to_id))
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model = self.model.to(self.device)
        self.model.eval()
        
        logger.info(f"成功加载域适应模型，特征维度: {feature_dim}")
        logger.info(f"支持的类别: {list(self.cat_to_id.keys())}")
    
    def extract_features(self, image_path: str) -> np.ndarray:
        """提取单张图像的特征"""
        try:
            image = Image.open(image_path).convert('RGB')
            image_tensor = self.transform(image).unsqueeze(0).to(self.device)
            
            with torch.no_grad():
                features = self.model(image_tensor, return_features=True)
                return features.cpu().numpy().flatten()
        except Exception as e:
            logger.error(f"提取特征失败 {image_path}: {e}")
            return np.zeros(2048)
    
    def create_random_splits(self, annotations_path: str, original_dir: str, infrared_dir: str, 
                           num_rounds: int = 10, test_ratio: float = 0.3) -> List[Dict]:
        """创建多轮随机数据分割"""
        logger.info(f"创建 {num_rounds} 轮随机数据分割...")
        
        # 加载标注
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations_dict = json.load(f)
        
        # 获取所有有效的图像对
        valid_pairs = []
        for original_filename, annotation in annotations_dict.items():
            if annotation['category'] in self.cat_to_id:
                base_name = original_filename.replace('.jpg', '')
                infrared_filename = f"{base_name}_ir.jpg"
                
                original_path = os.path.join(original_dir, original_filename)
                infrared_path = os.path.join(infrared_dir, infrared_filename)
                
                if os.path.exists(original_path) and os.path.exists(infrared_path):
                    valid_pairs.append({
                        'original_file': original_filename,
                        'infrared_file': infrared_filename,
                        'category': annotation['category'],
                        'original_path': original_path,
                        'infrared_path': infrared_path
                    })
        
        logger.info(f"找到 {len(valid_pairs)} 个有效的图像对")
        
        # 按类别分组
        category_groups = {}
        for pair in valid_pairs:
            cat = pair['category']
            if cat not in category_groups:
                category_groups[cat] = []
            category_groups[cat].append(pair)
        
        # 创建多轮随机分割
        splits = []
        for round_idx in range(num_rounds):
            train_pairs = []
            test_pairs = []
            
            # 对每个类别进行分层采样
            for cat, pairs in category_groups.items():
                random.shuffle(pairs)
                split_idx = int(len(pairs) * (1 - test_ratio))
                
                train_pairs.extend(pairs[:split_idx])
                test_pairs.extend(pairs[split_idx:])
            
            splits.append({
                'round': round_idx + 1,
                'train_pairs': train_pairs,
                'test_pairs': test_pairs,
                'train_count': len(train_pairs),
                'test_count': len(test_pairs)
            })
        
        return splits
    
    def test_single_round(self, train_pairs: List[Dict], test_pairs: List[Dict]) -> Dict:
        """测试单轮数据分割"""
        
        # 构建参考数据库（使用训练集的原始图像）
        train_features = []
        train_labels = []
        
        for pair in train_pairs:
            features = self.extract_features(pair['original_path'])
            train_features.append(features)
            train_labels.append(self.cat_to_id[pair['category']])
        
        train_features = np.vstack(train_features)
        train_labels = np.array(train_labels)
        
        # 特征标准化
        train_features_scaled = self.scaler.fit_transform(train_features)
        
        # 测试红外图像
        test_predictions = []
        test_ground_truths = []
        test_confidences = []
        
        for pair in test_pairs:
            # 提取红外图像特征
            features = self.extract_features(pair['infrared_path'])
            features_scaled = self.scaler.transform(features.reshape(1, -1))
            
            # KNN分类
            knn = KNeighborsClassifier(n_neighbors=min(7, len(train_features)), metric='cosine')
            knn.fit(train_features_scaled, train_labels)
            
            pred_label = knn.predict(features_scaled)[0]
            pred_proba = knn.predict_proba(features_scaled)[0]
            confidence = max(pred_proba)
            
            predicted_cat = self.id_to_cat[pred_label]
            
            test_predictions.append(predicted_cat)
            test_ground_truths.append(pair['category'])
            test_confidences.append(confidence)
        
        # 计算指标
        accuracy = accuracy_score(test_ground_truths, test_predictions)
        
        # 按类别统计
        category_stats = {}
        for cat in self.cat_to_id.keys():
            cat_indices = [i for i, gt in enumerate(test_ground_truths) if gt == cat]
            if cat_indices:
                cat_preds = [test_predictions[i] for i in cat_indices]
                cat_acc = accuracy_score([test_ground_truths[i] for i in cat_indices], cat_preds)
                cat_conf = statistics.mean([test_confidences[i] for i in cat_indices])
                category_stats[cat] = {
                    'accuracy': cat_acc,
                    'confidence': cat_conf,
                    'count': len(cat_indices),
                    'correct': sum(1 for i in cat_indices if test_predictions[i] == test_ground_truths[i])
                }
        
        # 混淆矩阵
        cm = confusion_matrix(
            [self.cat_to_id[gt] for gt in test_ground_truths],
            [self.cat_to_id[pred] for pred in test_predictions],
            labels=list(range(len(self.cat_to_id)))
        )
        
        return {
            'accuracy': accuracy,
            'avg_confidence': statistics.mean(test_confidences),
            'confidence_std': statistics.stdev(test_confidences) if len(test_confidences) > 1 else 0,
            'category_stats': category_stats,
            'confusion_matrix': cm.tolist(),
            'test_count': len(test_pairs),
            'train_count': len(train_pairs)
        }
    
    def run_random_validation(self, annotations_path: str, original_dir: str, infrared_dir: str,
                            num_rounds: int = 10, test_ratio: float = 0.3) -> Dict:
        """运行随机验证测试"""
        logger.info(f"开始 {num_rounds} 轮随机验证测试...")
        
        # 创建随机分割
        splits = self.create_random_splits(annotations_path, original_dir, infrared_dir, num_rounds, test_ratio)
        
        # 测试每轮分割
        round_results = []
        accuracies = []
        confidences = []
        
        for split in splits:
            logger.info(f"测试第 {split['round']} 轮 (训练: {split['train_count']}, 测试: {split['test_count']})")
            
            result = self.test_single_round(split['train_pairs'], split['test_pairs'])
            result['round'] = split['round']
            
            round_results.append(result)
            accuracies.append(result['accuracy'])
            confidences.append(result['avg_confidence'])
            
            logger.info(f"  第 {split['round']} 轮准确率: {result['accuracy']:.4f}")
        
        # 统计总体结果
        overall_stats = {
            'num_rounds': num_rounds,
            'test_ratio': test_ratio,
            'accuracy_mean': statistics.mean(accuracies),
            'accuracy_std': statistics.stdev(accuracies) if len(accuracies) > 1 else 0,
            'accuracy_min': min(accuracies),
            'accuracy_max': max(accuracies),
            'confidence_mean': statistics.mean(confidences),
            'confidence_std': statistics.stdev(confidences) if len(confidences) > 1 else 0,
            'rounds_with_100_percent': sum(1 for acc in accuracies if acc >= 0.999),
            'rounds_with_95_percent': sum(1 for acc in accuracies if acc >= 0.95),
            'rounds_with_90_percent': sum(1 for acc in accuracies if acc >= 0.90)
        }
        
        # 按类别汇总统计
        category_summary = {}
        for cat in self.cat_to_id.keys():
            cat_accuracies = []
            cat_confidences = []
            cat_counts = []
            
            for result in round_results:
                if cat in result['category_stats']:
                    cat_stats = result['category_stats'][cat]
                    cat_accuracies.append(cat_stats['accuracy'])
                    cat_confidences.append(cat_stats['confidence'])
                    cat_counts.append(cat_stats['count'])
            
            if cat_accuracies:
                category_summary[cat] = {
                    'accuracy_mean': statistics.mean(cat_accuracies),
                    'accuracy_std': statistics.stdev(cat_accuracies) if len(cat_accuracies) > 1 else 0,
                    'accuracy_min': min(cat_accuracies),
                    'accuracy_max': max(cat_accuracies),
                    'confidence_mean': statistics.mean(cat_confidences),
                    'avg_test_count': statistics.mean(cat_counts)
                }
        
        return {
            'overall_stats': overall_stats,
            'category_summary': category_summary,
            'round_results': round_results
        }

def main():
    parser = argparse.ArgumentParser(description="随机验证测试")
    parser.add_argument("--model", type=str, required=True, help="域适应模型路径")
    parser.add_argument("--annotations", type=str, required=True, help="标注文件路径")
    parser.add_argument("--original-dir", type=str, required=True, help="原始图像目录")
    parser.add_argument("--infrared-dir", type=str, required=True, help="红外图像目录")
    parser.add_argument("--num-rounds", type=int, default=20, help="随机测试轮数")
    parser.add_argument("--test-ratio", type=float, default=0.3, help="测试集比例")
    parser.add_argument("--output", type=str, default="random_validation_results.json", help="结果输出文件")
    parser.add_argument("--device", type=str, default="auto", help="设备")
    
    args = parser.parse_args()
    
    # 设置随机种子以确保可重现性
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 创建测试器
    tester = RandomValidationTester(args.model, args.device)
    
    # 运行随机验证
    results = tester.run_random_validation(
        args.annotations, args.original_dir, args.infrared_dir, 
        args.num_rounds, args.test_ratio
    )
    
    # 保存结果
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logger.info(f"随机验证测试完成! 结果已保存: {args.output}")
    
    # 打印总结
    overall = results['overall_stats']
    print("\n" + "="*60)
    print("随机验证测试总结")
    print("="*60)
    print(f"测试轮数: {overall['num_rounds']}")
    print(f"测试集比例: {overall['test_ratio']:.1%}")
    print(f"平均准确率: {overall['accuracy_mean']:.4f} ± {overall['accuracy_std']:.4f}")
    print(f"准确率范围: {overall['accuracy_min']:.4f} - {overall['accuracy_max']:.4f}")
    print(f"100%准确率轮数: {overall['rounds_with_100_percent']}/{overall['num_rounds']}")
    print(f"95%+准确率轮数: {overall['rounds_with_95_percent']}/{overall['num_rounds']}")
    print(f"90%+准确率轮数: {overall['rounds_with_90_percent']}/{overall['num_rounds']}")
    
    print("\n按类别统计:")
    for cat, stats in results['category_summary'].items():
        print(f"  {cat}:")
        print(f"    平均准确率: {stats['accuracy_mean']:.4f} ± {stats['accuracy_std']:.4f}")
        print(f"    准确率范围: {stats['accuracy_min']:.4f} - {stats['accuracy_max']:.4f}")
        print(f"    平均测试样本数: {stats['avg_test_count']:.1f}")
    
    print("="*60)

if __name__ == "__main__":
    main()
