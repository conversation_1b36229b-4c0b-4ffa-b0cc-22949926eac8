{"models": {"pytorch": {"size_mb": 341.92339038848877, "path": "./compressed_domain_model.pth"}, "onnx": {"size_mb": 344.73975944519043, "path": "deployment/infrared_cat_model.onnx", "compression_ratio": 0.9918304489704518}, "quantized": {"size_mb": 89.30198001861572, "path": "deployment/infrared_cat_model_quantized.onnx", "compression_ratio": 3.8288444479866186}}, "files": {"onnx_model": "deployment/infrared_cat_model.onnx", "quantized_model": "deployment/infrared_cat_model_quantized.onnx", "reference_features": "deployment/reference_features.json", "inference_wrapper": "deployment/infrared_cat_recognizer.py"}, "usage": {"single_image": "python deployment/infrared_cat_recognizer.py --model deployment/infrared_cat_model.onnx --reference deployment/reference_features.json --image <image_path>", "batch_images": "python deployment/infrared_cat_recognizer.py --model deployment/infrared_cat_model.onnx --reference deployment/reference_features.json --images <image1> <image2> ...", "benchmark": "python deployment/infrared_cat_recognizer.py --model deployment/infrared_cat_model.onnx --reference deployment/reference_features.json --image <image_path> --benchmark"}}