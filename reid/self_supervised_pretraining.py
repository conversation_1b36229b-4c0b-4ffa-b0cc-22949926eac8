#!/usr/bin/env python3
"""
自监督预训练 + 微调 - 在大量猫咪图像上进行自监督预训练，然后微调
这种方法可能实现质的突破
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image, ImageFilter
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimCLRModel(nn.Module):
    """SimCLR自监督学习模型"""
    
    def __init__(self, base_model, feature_dim=2048, projection_dim=512):
        super(SimCLRModel, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        self.projection_dim = projection_dim
        
        # 解冻所有层进行端到端训练
        for param in self.feature_extractor.parameters():
            param.requires_grad = True
        
        # 投影头 - SimCLR的关键组件
        self.projection_head = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Linear(feature_dim, projection_dim)
        )
    
    def forward(self, x, return_features=False):
        # 特征提取
        features = self.feature_extractor(x)
        
        if return_features:
            return features
        
        # 投影
        projections = self.projection_head(features)
        
        return F.normalize(projections, dim=1)

class GaussianBlur:
    """高斯模糊增强"""
    
    def __init__(self, sigma_min=0.1, sigma_max=2.0):
        self.sigma_min = sigma_min
        self.sigma_max = sigma_max
    
    def __call__(self, img):
        sigma = random.uniform(self.sigma_min, self.sigma_max)
        return img.filter(ImageFilter.GaussianBlur(radius=sigma))

class SelfSupervisedDataset(Dataset):
    """自监督学习数据集"""
    
    def __init__(self, original_annotations: Dict, individual_data_dir: str, 
                 original_dir: str, max_individual_cats: int = 50):
        
        self.original_dir = original_dir
        self.individual_data_dir = individual_data_dir
        
        # 强数据增强 - SimCLR风格
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.RandomResizedCrop(224, scale=(0.2, 1.0)),
            transforms.RandomHorizontalFlip(0.5),
            transforms.RandomApply([transforms.ColorJitter(0.8, 0.8, 0.8, 0.2)], p=0.8),
            transforms.RandomGrayscale(p=0.2),
            transforms.RandomApply([GaussianBlur()], p=0.5),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 收集所有图像路径
        self.image_paths = []
        
        # 添加原始3猫图像
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                self.image_paths.append(os.path.join(original_dir, filename))
        
        # 添加个体猫咪图像
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        self.image_paths.append(os.path.join(folder_path, img_file))
        
        logger.info(f"自监督数据集: {len(self.image_paths)} 张图像")
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        
        try:
            image = Image.open(image_path).convert('RGB')
        except Exception as e:
            logger.error(f"无法加载图像 {image_path}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        # 生成两个不同的增强版本
        image1 = self.transform(image)
        image2 = self.transform(image)
        
        return image1, image2

class NTXentLoss(nn.Module):
    """NT-Xent损失函数 - SimCLR的核心损失"""
    
    def __init__(self, temperature=0.5):
        super(NTXentLoss, self).__init__()
        self.temperature = temperature
    
    def forward(self, z1, z2):
        """
        z1, z2: [batch_size, projection_dim] 归一化的投影特征
        """
        batch_size = z1.shape[0]
        
        # 拼接所有特征
        z = torch.cat([z1, z2], dim=0)  # [2*batch_size, projection_dim]
        
        # 计算相似度矩阵
        sim_matrix = torch.matmul(z, z.T) / self.temperature  # [2*batch_size, 2*batch_size]
        
        # 创建正样本掩码
        labels = torch.arange(batch_size).to(z.device)
        labels = torch.cat([labels, labels], dim=0)
        
        # 正样本：(i, i+batch_size) 和 (i+batch_size, i)
        positive_mask = torch.zeros_like(sim_matrix, dtype=torch.bool)
        for i in range(batch_size):
            positive_mask[i, i + batch_size] = True
            positive_mask[i + batch_size, i] = True
        
        # 负样本：除了自己和正样本之外的所有样本
        negative_mask = ~torch.eye(2 * batch_size, dtype=torch.bool).to(z.device)
        negative_mask = negative_mask & ~positive_mask
        
        # 计算损失
        losses = []
        for i in range(2 * batch_size):
            # 正样本logit
            pos_logits = sim_matrix[i][positive_mask[i]]
            
            # 负样本logits
            neg_logits = sim_matrix[i][negative_mask[i]]
            
            # 组合logits
            logits = torch.cat([pos_logits, neg_logits])
            
            # 标签（正样本在前）
            target = torch.zeros(len(logits), dtype=torch.long).to(z.device)
            
            # 计算交叉熵损失
            loss = F.cross_entropy(logits.unsqueeze(0), target[:1])
            losses.append(loss)
        
        return torch.stack(losses).mean()

def simclr_pretrain_epoch(model, dataloader, optimizer, criterion, device, epoch):
    """SimCLR预训练一个epoch"""
    
    model.train()
    
    total_loss = 0.0
    num_batches = 0
    
    for image1, image2 in dataloader:
        image1 = image1.to(device)
        image2 = image2.to(device)
        
        optimizer.zero_grad()
        
        # 前向传播
        z1 = model(image1)
        z2 = model(image2)
        
        # 计算NT-Xent损失
        loss = criterion(z1, z2)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        num_batches += 1
    
    avg_loss = total_loss / num_batches
    logger.info(f"SimCLR预训练 Epoch {epoch+1} - 损失: {avg_loss:.4f}")
    
    return avg_loss

class FineTuneModel(nn.Module):
    """微调模型"""
    
    def __init__(self, pretrained_simclr_model, num_classes=33):
        super(FineTuneModel, self).__init__()
        
        # 使用预训练的特征提取器
        self.feature_extractor = pretrained_simclr_model.feature_extractor
        self.feature_dim = pretrained_simclr_model.feature_dim
        self.num_classes = num_classes
        
        # 冻结大部分层，只微调最后几层
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 解冻最后一个stage
        for param in self.feature_extractor.backbone.layers[-1].parameters():
            param.requires_grad = True
        for param in self.feature_extractor.backbone.norm.parameters():
            param.requires_grad = True
        
        # 新的分类头
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_dim, self.feature_dim),
            nn.BatchNorm1d(self.feature_dim),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(self.feature_dim, num_classes)
        )
    
    def forward(self, x, return_features=False):
        features = self.feature_extractor(x)
        
        if return_features:
            return features
        
        logits = self.classifier(features)
        return logits, features

def main():
    parser = argparse.ArgumentParser(description='自监督预训练 + 微调')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='self_supervised_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=50, help='最大个体猫咪数量')
    parser.add_argument('--pretrain-epochs', type=int, default=100, help='预训练轮数')
    parser.add_argument('--finetune-epochs', type=int, default=50, help='微调轮数')
    parser.add_argument('--batch-size', type=int, default=32, help='批次大小')
    parser.add_argument('--lr', type=float, default=1e-3, help='学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据准备
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    # 创建自监督数据集
    ssl_dataset = SelfSupervisedDataset(
        annotations, args.individual_data_dir, args.original_dir, args.max_individual_cats
    )
    
    ssl_dataloader = DataLoader(ssl_dataset, batch_size=args.batch_size, 
                               shuffle=True, num_workers=4, drop_last=True)
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    # 创建基础特征提取器
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从对比学习模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建SimCLR模型
    simclr_model = SimCLRModel(base_model, feature_dim)
    simclr_model = simclr_model.to(device)
    
    # SimCLR预训练
    logger.info("开始SimCLR自监督预训练...")
    
    optimizer = optim.AdamW(simclr_model.parameters(), lr=args.lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.pretrain_epochs)
    criterion = NTXentLoss(temperature=0.5)
    
    for epoch in range(args.pretrain_epochs):
        loss = simclr_pretrain_epoch(simclr_model, ssl_dataloader, optimizer, criterion, device, epoch)
        scheduler.step()
        
        # 每20轮保存一次
        if (epoch + 1) % 20 == 0:
            torch.save({
                'model_state_dict': simclr_model.state_dict(),
                'feature_dim': feature_dim,
                'epoch': epoch
            }, f'simclr_pretrained_epoch_{epoch+1}.pth')
    
    logger.info("SimCLR预训练完成！")
    
    # 保存预训练模型
    torch.save({
        'model_state_dict': simclr_model.state_dict(),
        'feature_dim': feature_dim
    }, 'simclr_pretrained_final.pth')
    
    logger.info("自监督预训练模型已保存")

if __name__ == "__main__":
    main()
