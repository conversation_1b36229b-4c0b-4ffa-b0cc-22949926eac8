#!/usr/bin/env python3
"""
集成学习增强 - 结合多个模型的预测结果，进一步提升准确率
"""

import os
import sys
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import VotingClassifier
from sklearn.linear_model import LogisticRegression

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel, ProgressiveDataset
from contrastive_learning_enhancement import ContrastiveProgressiveModel

def load_ensemble_models(model_paths: List[str], device: torch.device):
    """加载集成模型"""
    
    models = []
    
    for model_path in model_paths:
        if not os.path.exists(model_path):
            print(f"⚠️  模型文件不存在: {model_path}")
            continue
        
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            feature_dim = checkpoint.get('feature_dim', 2048)
            num_classes = checkpoint.get('num_classes', 33)
            
            # 创建基础特征提取器
            base_model = FeatureExtractorModel(feature_dim=feature_dim)
            
            # 根据模型类型创建相应的模型
            if 'projection_head' in checkpoint['model_state_dict']:
                # 对比学习模型
                model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
            else:
                # 渐进式模型
                model = ProgressiveModel(base_model, feature_dim, num_classes)
            
            model.load_state_dict(checkpoint['model_state_dict'])
            model = model.to(device)
            model.eval()
            
            models.append({
                'model': model,
                'name': os.path.basename(model_path),
                'feature_dim': feature_dim
            })
            
            print(f"✅ 成功加载模型: {os.path.basename(model_path)}")
            
        except Exception as e:
            print(f"❌ 加载模型失败 {model_path}: {e}")
    
    return models

def extract_ensemble_features(models, dataloader, device):
    """提取集成特征"""
    
    all_features_list = []  # 每个模型的特征
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in dataloader:
            images = images.to(device)
            
            batch_features_list = []
            
            # 从每个模型提取特征
            for model_info in models:
                model = model_info['model']
                features = model(images, return_features=True)
                batch_features_list.append(features.cpu().numpy())
            
            # 第一个batch时初始化
            if len(all_features_list) == 0:
                all_features_list = [[] for _ in range(len(models))]
            
            # 添加到对应模型的特征列表
            for i, features in enumerate(batch_features_list):
                all_features_list[i].append(features)
            
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    # 合并特征
    ensemble_features_list = []
    for features_list in all_features_list:
        if features_list:
            ensemble_features_list.append(np.vstack(features_list))
    
    return ensemble_features_list, np.array(all_labels), np.array(all_is_original)

def ensemble_predict(ensemble_features_list, labels, method='weighted_voting'):
    """集成预测"""
    
    if len(ensemble_features_list) == 0:
        return np.array([])
    
    predictions_list = []
    
    # 每个模型单独预测
    for i, features in enumerate(ensemble_features_list):
        knn = KNeighborsClassifier(n_neighbors=min(9, len(set(labels))), 
                                  metric='cosine', weights='distance')
        
        # 留一法交叉验证
        predictions = []
        for j in range(len(features)):
            train_mask = np.ones(len(features), dtype=bool)
            train_mask[j] = False
            
            if train_mask.sum() > 0:
                knn.fit(features[train_mask], labels[train_mask])
                pred = knn.predict(features[j:j+1])
                predictions.append(pred[0])
            else:
                predictions.append(labels[j])
        
        predictions_list.append(np.array(predictions))
    
    if method == 'simple_voting':
        # 简单投票
        final_predictions = []
        for i in range(len(labels)):
            votes = [pred[i] for pred in predictions_list]
            final_predictions.append(max(set(votes), key=votes.count))
        return np.array(final_predictions)
    
    elif method == 'weighted_voting':
        # 加权投票 (基于模型在验证集上的表现)
        model_weights = []
        for predictions in predictions_list:
            acc = accuracy_score(labels, predictions)
            model_weights.append(acc)
        
        # 归一化权重
        total_weight = sum(model_weights)
        if total_weight > 0:
            model_weights = [w / total_weight for w in model_weights]
        else:
            model_weights = [1.0 / len(model_weights)] * len(model_weights)
        
        final_predictions = []
        for i in range(len(labels)):
            vote_counts = {}
            for j, predictions in enumerate(predictions_list):
                pred = predictions[i]
                if pred not in vote_counts:
                    vote_counts[pred] = 0
                vote_counts[pred] += model_weights[j]
            
            # 选择得票最多的类别
            best_pred = max(vote_counts.items(), key=lambda x: x[1])[0]
            final_predictions.append(best_pred)
        
        return np.array(final_predictions)
    
    elif method == 'feature_fusion':
        # 特征级融合
        if len(ensemble_features_list) > 1:
            # 标准化特征
            normalized_features_list = []
            for features in ensemble_features_list:
                scaler = StandardScaler()
                normalized_features = scaler.fit_transform(features)
                normalized_features_list.append(normalized_features)
            
            # 特征拼接
            fused_features = np.hstack(normalized_features_list)
            
            # 使用融合特征进行预测
            knn = KNeighborsClassifier(n_neighbors=min(11, len(set(labels))), 
                                      metric='cosine', weights='distance')
            
            predictions = []
            for i in range(len(fused_features)):
                train_mask = np.ones(len(fused_features), dtype=bool)
                train_mask[i] = False
                
                if train_mask.sum() > 0:
                    knn.fit(fused_features[train_mask], labels[train_mask])
                    pred = knn.predict(fused_features[i:i+1])
                    predictions.append(pred[0])
                else:
                    predictions.append(labels[i])
            
            return np.array(predictions)
        else:
            # 只有一个模型，直接返回其预测
            return predictions_list[0]

def comprehensive_ensemble_evaluation(model_paths: List[str], test_data_path: str):
    """综合集成评估"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 加载集成模型
    print("📦 加载集成模型...")
    models = load_ensemble_models(model_paths, device)
    
    if len(models) == 0:
        print("❌ 没有可用的模型")
        return
    
    print(f"✅ 成功加载 {len(models)} 个模型")
    
    # 准备测试数据
    print("📁 准备测试数据...")
    
    with open('../tagging/annotations.json', 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    test_dataset = ProgressiveDataset(
        annotations, '../dataset/cat_individual_images', '../dataset/renamed_thumbnails', 
        cat_to_id, max_individual_cats=30, is_training=False
    )
    
    # 随机采样测试数据
    test_indices = random.sample(range(len(test_dataset)), min(600, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=16, shuffle=False, num_workers=2)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    # 提取集成特征
    print("🔍 提取集成特征...")
    ensemble_features_list, labels, is_original = extract_ensemble_features(models, test_loader, device)
    
    # 测试不同集成方法
    ensemble_methods = ['simple_voting', 'weighted_voting', 'feature_fusion']
    
    results = {}
    
    for method in ensemble_methods:
        print(f"\n=== 测试集成方法: {method} ===")
        
        predictions = ensemble_predict(ensemble_features_list, labels, method)
        
        if len(predictions) == 0:
            print("❌ 预测失败")
            continue
        
        # 计算准确率
        overall_acc = accuracy_score(labels, predictions)
        
        original_mask = is_original.astype(bool)
        original_acc = accuracy_score(labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
        
        individual_mask = ~original_mask
        individual_acc = accuracy_score(labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
        
        print(f"📊 {method} 结果:")
        print(f"  总体准确率: {overall_acc:.4f} ({overall_acc*100:.2f}%)")
        print(f"  原始3猫准确率: {original_acc:.4f} ({original_acc*100:.2f}%)")
        print(f"  个体猫咪准确率: {individual_acc:.4f} ({individual_acc*100:.2f}%)")
        
        # 性能评级
        if original_acc >= 0.95 and individual_acc >= 0.70:
            grade = "🏆 优秀"
        elif original_acc >= 0.90 and individual_acc >= 0.60:
            grade = "🥈 良好"
        elif original_acc >= 0.85 and individual_acc >= 0.50:
            grade = "🥉 一般"
        else:
            grade = "❌ 需改进"
        
        print(f"  综合评级: {grade}")
        
        results[method] = {
            'overall_acc': overall_acc,
            'original_acc': original_acc,
            'individual_acc': individual_acc,
            'grade': grade
        }
    
    # 找出最佳方法
    best_method = max(results.items(), key=lambda x: x[1]['individual_acc'])
    
    print(f"\n🎯 最佳集成方法: {best_method[0]}")
    print(f"   个体猫咪准确率: {best_method[1]['individual_acc']*100:.2f}%")
    print(f"   原始3猫准确率: {best_method[1]['original_acc']*100:.2f}%")
    
    # 保存结果
    output_file = "ensemble_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: {output_file}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='集成学习评估')
    parser.add_argument('--models', nargs='+', required=True, help='模型文件路径列表')
    parser.add_argument('--test-data', default='../tagging/annotations.json', help='测试数据路径')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    print("🚀 开始集成学习评估...")
    results = comprehensive_ensemble_evaluation(args.models, args.test_data)
    print("✅ 集成学习评估完成！")

if __name__ == "__main__":
    main()
