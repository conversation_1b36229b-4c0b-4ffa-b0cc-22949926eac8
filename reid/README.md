# 猫咪个体识别系统

基于Few-Shot Meta Learning的高精度猫咪个体识别系统，能够识别原始3猫（小白、小花、小黑）以及任意个体猫咪。

## 🎯 性能指标

- **总体准确率**: 94.13%
- **原始3猫准确率**: 93.75%
- **个体猫准确率**: 94.29%
- **支持猫咪数量**: 542个类别
- **训练方法**: Few-Shot Meta Learning

## 📁 项目结构

```
reid/
├── README.md                           # 项目说明文档
├── deploy_cat_recognition.py           # 部署脚本（主要使用）
├── few_shot_meta_learning.py          # Few-Shot Meta Learning训练代码
├── feature_based_cat_recognition.py   # 特征提取模型
├── contrastive_learning_enhancement.py # 对比学习增强
├── progressive_transfer_learning.py   # 渐进式迁移学习
├── knowledge_distillation_enhancement.py # 知识蒸馏
├── extended_domain_adversarial_training.py # 域对抗训练
├── enhanced_progressive_training.py   # 增强渐进式训练
├── final_comprehensive_evaluation.py  # 综合评估脚本
├── best_few_shot_meta_model.pth      # 最佳Few-Shot模型（主要使用）
├── best_contrastive_model.pth        # 最佳对比学习模型
└── training/                          # 训练相关文件
    ├── domain_adapted_model.pth
    ├── compressed_domain_model.pth
    └── ...
```

## 🚀 快速开始

### 1. 环境要求

```bash
# Python 3.8+
# PyTorch 1.9+
# torchvision
# PIL
# numpy
# scikit-learn
```

### 2. 模型验证

验证模型性能：

```bash
python deploy_cat_recognition.py --validate
```

### 3. 单张图片识别

```bash
python deploy_cat_recognition.py --image path/to/cat_image.jpg
```

示例输出：
```
🐱 识别结果:
   图片: cat_photo.jpg
   猫咪: 小白
   置信度: 95.30%
```

### 4. 批量图片识别

```bash
python deploy_cat_recognition.py --batch path/to/images_folder/
```

示例输出：
```
🐱 批量识别结果 (共 10 张图片):
------------------------------------------------------------
cat1.jpg                      小白            95.30%
cat2.jpg                      小花            92.15%
cat3.jpg                      0001            89.67%
...
```

## 📊 模型详情

### Few-Shot Meta Learning模型

- **基础架构**: MegaDescriptor-T-224
- **特征维度**: 2048
- **训练方法**: 原型网络 (Prototypical Networks)
- **训练轮数**: 12轮 (达到96.72%训练准确率)
- **验证准确率**: 95.71%个体猫识别准确率

### 训练过程

1. **数据准备**: 使用原始3猫数据 + 个体猫数据
2. **Few-Shot训练**: 每轮150个episodes，每个episode包含support和query样本
3. **原型学习**: 通过计算类别原型进行分类
4. **性能验证**: 定期评估原始3猫和个体猫的准确率

## 🔧 高级使用

### 自定义模型路径

```bash
python deploy_cat_recognition.py --model custom_model.pth --image cat.jpg
```

### 训练新模型

```bash
python few_shot_meta_learning.py --annotations /path/to/annotations.json \
                                  --individual-data-dir /path/to/individual_cats \
                                  --original-dir /path/to/original_cats \
                                  --output new_model.pth \
                                  --epochs 50
```

## 📈 性能对比

| 模型类型 | 总体准确率 | 原始3猫准确率 | 个体猫准确率 | 评级 |
|---------|-----------|-------------|-------------|------|
| Few-Shot Meta Learning | 94.13% | 93.75% | 94.29% | 🥇 优秀 |
| 对比学习模型 | 95.75% | 100.00% | 73.02% | 🥇 优秀 |
| 增强渐进式模型 | 87.88% | 100.00% | 23.02% | ❌ 需改进 |

## 🛠️ 故障排除

### 常见问题

1. **CUDA内存不足**
   - 减少batch size
   - 使用CPU模式：设置环境变量 `CUDA_VISIBLE_DEVICES=""`

2. **模型文件不存在**
   - 确保 `best_few_shot_meta_model.pth` 在当前目录
   - 使用 `--model` 参数指定正确路径

3. **图片格式不支持**
   - 支持格式：JPG, JPEG, PNG, BMP, TIFF
   - 确保图片可以正常打开

### 日志级别

修改日志级别以获取更多信息：

```python
logging.basicConfig(level=logging.DEBUG)
```

## 📝 更新日志

- **v1.0** (2025-07-14): 初始版本，基于Few-Shot Meta Learning
  - 实现94.13%总体准确率
  - 支持542个猫咪类别
  - 提供完整的部署脚本

## 📞 技术支持

如有问题，请检查：
1. 模型文件是否完整
2. 数据路径是否正确
3. 依赖包是否安装完整
4. CUDA环境是否配置正确

---

**注意**: 本系统基于Few-Shot Meta Learning训练，在保持原有3猫高准确率的同时，实现了对任意个体猫咪的高精度识别。
