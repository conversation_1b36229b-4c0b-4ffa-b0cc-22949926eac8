#!/usr/bin/env python3
"""
增强的渐进式训练 - 通过更强的数据增强和优化策略提升泛化能力
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score
from sklearn.neighbors import KNeighborsClassifier

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedProgressiveDataset(Dataset):
    """增强的渐进式数据集 - 更强的数据增强"""
    
    def __init__(self, original_annotations: Dict, individual_data_dir: str, 
                 original_dir: str, cat_to_id: Dict[str, int], 
                 max_individual_cats: int = 20, is_training=True):
        
        self.original_dir = original_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 更强的数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.RandomRotation(15),  # 增加旋转
                transforms.ColorJitter(brightness=0.3, contrast=0.3, saturation=0.3, hue=0.15),  # 增强颜色变化
                transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),  # 增加仿射变换
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                transforms.RandomErasing(p=0.2, scale=(0.02, 0.1))  # 随机擦除
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        self.samples = []
        
        # 处理原始3猫数据 - 适度增加权重
        original_weight = 2  # 减少到2倍，给个体数据更多机会
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                for _ in range(original_weight):
                    self.samples.append({
                        'image_path': os.path.join(original_dir, filename),
                        'category': annotation['category'],
                        'label': cat_to_id[annotation['category']],
                        'is_original': True
                    })
        
        # 处理cat_individual_images数据 - 增加重复次数
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            individual_weight = 2  # 个体数据也重复2次
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                images = []
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        images.append(os.path.join(folder_path, img_file))
                
                # 为每张图像重复多次以增加训练机会
                for img_path in images:
                    for _ in range(individual_weight):
                        self.samples.append({
                            'image_path': img_path,
                            'category': cat_name,
                            'label': cat_to_id[cat_name],
                            'is_original': False
                        })
        
        # 随机打乱样本
        random.shuffle(self.samples)
        
        # 统计信息
        original_count = sum(1 for s in self.samples if s['is_original'])
        individual_count = len(self.samples) - original_count
        logger.info(f"增强数据集: 原始3猫 {original_count} 样本, 个体猫咪 {individual_count} 样本")
        logger.info(f"总类别数: {len(cat_to_id)}")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        
        try:
            image = Image.open(sample['image_path']).convert('RGB')
            
            # 训练时额外的图像增强
            if self.is_training and not sample['is_original'] and random.random() < 0.3:
                # 对个体猫咪图像进行额外增强
                if random.random() < 0.5:
                    enhancer = ImageEnhance.Brightness(image)
                    image = enhancer.enhance(random.uniform(0.8, 1.2))
                
                if random.random() < 0.3:
                    image = image.filter(ImageFilter.GaussianBlur(radius=random.uniform(0.5, 1.0)))
                
        except Exception as e:
            logger.error(f"无法加载图像 {sample['image_path']}: {e}")
            image = Image.new('RGB', (224, 224), color=(128, 128, 128))
        
        image = self.transform(image)
        return image, sample['label'], sample['category'], sample['is_original']

def enhanced_train_progressive_model(model, train_loader, val_loader, device, epochs, lr):
    """增强的渐进式训练"""
    
    # 分阶段训练策略
    stage1_epochs = epochs // 3  # 第一阶段：较高学习率
    stage2_epochs = epochs // 3  # 第二阶段：中等学习率
    stage3_epochs = epochs - stage1_epochs - stage2_epochs  # 第三阶段：较低学习率
    
    # 优化器设置
    trainable_params = [p for p in model.parameters() if p.requires_grad]
    
    best_val_acc = 0.0
    patience = 12
    patience_counter = 0
    
    # 第一阶段：较高学习率，重点学习个体特征
    logger.info(f"第一阶段训练 ({stage1_epochs} epochs) - 学习率: {lr}")
    optimizer = optim.AdamW(trainable_params, lr=lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=stage1_epochs)
    
    for epoch in range(stage1_epochs):
        train_epoch(model, train_loader, optimizer, device, epoch, stage1_epochs, "第一阶段", weight_ratio=1.5)
        scheduler.step()
        
        val_acc, original_acc, individual_acc = evaluate_progressive_model(model, val_loader, device)
        logger.info(f"第一阶段 Epoch {epoch+1}/{stage1_epochs} - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
        
        combined_score = 0.6 * original_acc + 0.4 * individual_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            save_best_model(model, epoch, val_acc, original_acc, individual_acc, "stage1")
        else:
            patience_counter += 1
    
    # 第二阶段：中等学习率，平衡训练
    logger.info(f"第二阶段训练 ({stage2_epochs} epochs) - 学习率: {lr*0.3}")
    optimizer = optim.AdamW(trainable_params, lr=lr*0.3, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=stage2_epochs)
    
    for epoch in range(stage2_epochs):
        train_epoch(model, train_loader, optimizer, device, epoch, stage2_epochs, "第二阶段", weight_ratio=1.8)
        scheduler.step()
        
        val_acc, original_acc, individual_acc = evaluate_progressive_model(model, val_loader, device)
        logger.info(f"第二阶段 Epoch {epoch+1}/{stage2_epochs} - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
        
        combined_score = 0.7 * original_acc + 0.3 * individual_acc
        if combined_score > best_val_acc:
            best_val_acc = combined_score
            patience_counter = 0
            save_best_model(model, epoch, val_acc, original_acc, individual_acc, "stage2")
        else:
            patience_counter += 1
            if patience_counter >= patience:
                logger.info("早停触发")
                break
    
    # 第三阶段：低学习率，精细调优
    if patience_counter < patience:
        logger.info(f"第三阶段训练 ({stage3_epochs} epochs) - 学习率: {lr*0.1}")
        optimizer = optim.AdamW(trainable_params, lr=lr*0.1, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=stage3_epochs)
        
        for epoch in range(stage3_epochs):
            train_epoch(model, train_loader, optimizer, device, epoch, stage3_epochs, "第三阶段", weight_ratio=2.0)
            scheduler.step()
            
            val_acc, original_acc, individual_acc = evaluate_progressive_model(model, val_loader, device)
            logger.info(f"第三阶段 Epoch {epoch+1}/{stage3_epochs} - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
            
            combined_score = 0.75 * original_acc + 0.25 * individual_acc
            if combined_score > best_val_acc:
                best_val_acc = combined_score
                patience_counter = 0
                save_best_model(model, epoch, val_acc, original_acc, individual_acc, "stage3")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info("早停触发")
                    break
    
    logger.info(f"增强训练完成！最佳综合得分: {best_val_acc:.4f}")
    return model

def train_epoch(model, train_loader, optimizer, device, epoch, total_epochs, stage_name, weight_ratio=1.5):
    """训练一个epoch"""
    
    model.train()
    criterion = nn.CrossEntropyLoss(reduction='none')
    
    total_loss = 0.0
    num_batches = 0
    
    for images, labels, categories, is_original in train_loader:
        images = images.to(device)
        labels = labels.to(device)
        is_original = is_original.to(device)
        
        optimizer.zero_grad()
        
        logits, features = model(images)
        losses = criterion(logits, labels)
        
        # 动态权重调整
        weights = torch.where(is_original, weight_ratio, 1.0)
        weighted_loss = (losses * weights).mean()
        
        # 添加特征正则化
        feature_reg = 0.001 * torch.norm(features, p=2, dim=1).mean()
        total_loss_with_reg = weighted_loss + feature_reg
        
        total_loss_with_reg.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += weighted_loss.item()
        num_batches += 1
    
    avg_loss = total_loss / num_batches
    logger.info(f"{stage_name} Epoch {epoch+1}/{total_epochs} - 训练损失: {avg_loss:.4f}")

def evaluate_progressive_model(model, val_loader, device):
    """评估模型性能"""
    
    model.eval()
    
    all_features = []
    all_labels = []
    all_is_original = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in val_loader:
            images = images.to(device)
            features = model(images, return_features=True)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        return 0.0, 0.0, 0.0
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # KNN分类
    knn = KNeighborsClassifier(n_neighbors=min(7, len(set(all_labels))), metric='cosine')
    
    predictions = []
    for i in range(len(all_features)):
        train_mask = np.ones(len(all_features), dtype=bool)
        train_mask[i] = False
        
        if train_mask.sum() > 0:
            knn.fit(all_features[train_mask], all_labels[train_mask])
            pred = knn.predict(all_features[i:i+1])
            predictions.append(pred[0])
        else:
            predictions.append(all_labels[i])
    
    predictions = np.array(predictions)
    
    overall_acc = accuracy_score(all_labels, predictions)
    
    original_mask = all_is_original.astype(bool)
    original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
    
    individual_mask = ~original_mask
    individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
    
    return overall_acc, original_acc, individual_acc

def save_best_model(model, epoch, val_acc, original_acc, individual_acc, stage):
    """保存最佳模型"""
    
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': model.feature_dim,
        'num_classes': model.num_classes,
        'epoch': epoch,
        'val_acc': val_acc,
        'original_acc': original_acc,
        'individual_acc': individual_acc,
        'stage': stage
    }, f'best_enhanced_progressive_model_{stage}.pth')

def main():
    parser = argparse.ArgumentParser(description='增强的渐进式训练')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='enhanced_progressive_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=20, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=30, help='总训练轮数')
    parser.add_argument('--batch-size', type=int, default=12, help='批次大小')
    parser.add_argument('--lr', type=float, default=2e-4, help='初始学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 加载数据
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建增强数据集
    full_dataset = EnhancedProgressiveDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    # 数据分割
    samples_by_cat = defaultdict(list)
    for i, sample in enumerate(full_dataset.samples):
        samples_by_cat[sample['label']].append(i)
    
    train_indices = []
    val_indices = []
    
    for cat_id, indices in samples_by_cat.items():
        random.shuffle(indices)
        split_idx = max(1, int(len(indices) * 0.85))  # 增加训练数据比例
        train_indices.extend(indices[:split_idx])
        val_indices.extend(indices[split_idx:])
    
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    
    logger.info(f"训练集: {len(train_dataset)} 样本")
    logger.info(f"验证集: {len(val_dataset)} 样本")
    
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从域适应模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建增强模型
    model = ProgressiveModel(base_model, feature_dim, len(cat_to_id))
    model = model.to(device)
    
    logger.info("开始增强的渐进式训练...")
    model = enhanced_train_progressive_model(model, train_loader, val_loader, device, args.epochs, args.lr)
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'num_classes': len(cat_to_id),
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"增强模型已保存到: {args.output}")

if __name__ == "__main__":
    main()
