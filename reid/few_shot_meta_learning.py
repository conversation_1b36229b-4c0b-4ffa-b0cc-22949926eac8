#!/usr/bin/env python3
"""
Few-Shot Meta Learning - 专门针对小样本猫咪识别的元学习方案
这是最有希望突破95%准确率的方法
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
from collections import defaultdict
import itertools

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import torch.nn.functional as F
import numpy as np
from PIL import Image
import torchvision.transforms as transforms
from sklearn.metrics import accuracy_score

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrototypicalNetwork(nn.Module):
    """原型网络 - Few-Shot Learning的经典方法"""
    
    def __init__(self, base_model, feature_dim=2048):
        super(PrototypicalNetwork, self).__init__()
        self.feature_extractor = base_model
        self.feature_dim = feature_dim
        
        # 解冻更多层以获得更强的表示能力
        for param in self.feature_extractor.backbone.parameters():
            param.requires_grad = False
        
        # 解冻最后两个stage
        for param in self.feature_extractor.backbone.layers[-2:].parameters():
            param.requires_grad = True
        for param in self.feature_extractor.backbone.norm.parameters():
            param.requires_grad = True
        
        # 专门为Few-Shot设计的特征增强器
        self.few_shot_enhancer = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.BatchNorm1d(feature_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(feature_dim * 2, feature_dim),
            nn.BatchNorm1d(feature_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            # 输出L2归一化的特征
            nn.Linear(feature_dim, feature_dim)
        )
    
    def forward(self, x):
        # 特征提取
        base_features = self.feature_extractor(x)
        enhanced_features = self.few_shot_enhancer(base_features)
        
        # L2归一化 - 这对原型网络很重要
        normalized_features = F.normalize(enhanced_features, p=2, dim=1)
        
        return normalized_features
    
    def compute_prototypes(self, support_features, support_labels):
        """计算每个类别的原型"""
        unique_labels = torch.unique(support_labels)
        prototypes = []
        
        for label in unique_labels:
            mask = support_labels == label
            class_features = support_features[mask]
            prototype = class_features.mean(dim=0)
            prototypes.append(prototype)
        
        return torch.stack(prototypes), unique_labels
    
    def classify_by_prototypes(self, query_features, prototypes, prototype_labels):
        """基于原型进行分类"""
        # 计算查询样本与所有原型的距离
        distances = torch.cdist(query_features, prototypes, p=2)
        
        # 转换为相似度（距离越小相似度越高）
        similarities = -distances
        
        # 预测类别
        predicted_indices = similarities.argmax(dim=1)
        predicted_labels = prototype_labels[predicted_indices]
        
        return predicted_labels, similarities

class FewShotDataset(Dataset):
    """Few-Shot数据集"""
    
    def __init__(self, original_annotations: Dict, individual_data_dir: str, 
                 original_dir: str, cat_to_id: Dict[str, int], 
                 max_individual_cats: int = 30, is_training=True):
        
        self.original_dir = original_dir
        self.individual_data_dir = individual_data_dir
        self.cat_to_id = cat_to_id
        self.is_training = is_training
        
        # 更强的数据增强
        if is_training:
            self.transform = transforms.Compose([
                transforms.Resize((256, 256)),
                transforms.RandomCrop((224, 224)),
                transforms.RandomHorizontalFlip(0.5),
                transforms.RandomRotation(20),
                transforms.ColorJitter(brightness=0.4, contrast=0.4, saturation=0.4, hue=0.2),
                transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.8, 1.2)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                transforms.RandomErasing(p=0.3, scale=(0.02, 0.15))
            ])
        else:
            self.transform = transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
        
        # 按类别组织数据
        self.samples_by_class = defaultdict(list)
        
        # 处理原始3猫数据
        for filename, annotation in original_annotations.items():
            if annotation['category'] in ['小白', '小花', '小黑']:
                self.samples_by_class[cat_to_id[annotation['category']]].append({
                    'image_path': os.path.join(original_dir, filename),
                    'category': annotation['category'],
                    'label': cat_to_id[annotation['category']],
                    'is_original': True
                })
        
        # 处理个体猫咪数据
        if os.path.exists(individual_data_dir):
            individual_folders = sorted([f for f in os.listdir(individual_data_dir) 
                                       if os.path.isdir(os.path.join(individual_data_dir, f)) 
                                       and f.isdigit()])
            
            if max_individual_cats:
                individual_folders = individual_folders[:max_individual_cats]
            
            for cat_folder in individual_folders:
                cat_name = f"individual_{cat_folder}"
                
                if cat_name not in cat_to_id:
                    cat_to_id[cat_name] = len(cat_to_id)
                
                folder_path = os.path.join(individual_data_dir, cat_folder)
                for img_file in os.listdir(folder_path):
                    if img_file.lower().endswith(('.jpg', '.jpeg', '.png')):
                        self.samples_by_class[cat_to_id[cat_name]].append({
                            'image_path': os.path.join(folder_path, img_file),
                            'category': cat_name,
                            'label': cat_to_id[cat_name],
                            'is_original': False
                        })
        
        # 统计信息
        self.classes = list(self.samples_by_class.keys())
        total_samples = sum(len(samples) for samples in self.samples_by_class.values())
        
        logger.info(f"Few-Shot数据集: {len(self.classes)} 个类别, {total_samples} 个样本")
        for class_id, samples in self.samples_by_class.items():
            logger.info(f"  类别 {class_id}: {len(samples)} 个样本")
    
    def sample_episode(self, n_way=5, k_shot=5, q_query=10):
        """采样一个Few-Shot episode"""
        
        # 随机选择n_way个类别
        if len(self.classes) < n_way:
            selected_classes = self.classes
        else:
            selected_classes = random.sample(self.classes, n_way)
        
        support_images = []
        support_labels = []
        query_images = []
        query_labels = []
        
        for i, class_id in enumerate(selected_classes):
            class_samples = self.samples_by_class[class_id]
            
            # 确保有足够的样本
            if len(class_samples) < k_shot + q_query:
                # 如果样本不够，就重复采样
                selected_samples = random.choices(class_samples, k=k_shot + q_query)
            else:
                selected_samples = random.sample(class_samples, k_shot + q_query)
            
            # 分割support和query
            support_samples = selected_samples[:k_shot]
            query_samples = selected_samples[k_shot:k_shot + q_query]
            
            # 加载support图像
            for sample in support_samples:
                try:
                    image = Image.open(sample['image_path']).convert('RGB')
                    image = self.transform(image)
                    support_images.append(image)
                    support_labels.append(i)  # 使用episode内的标签
                except Exception as e:
                    logger.error(f"无法加载图像 {sample['image_path']}: {e}")
                    # 创建一个随机图像作为备用
                    image = torch.randn(3, 224, 224)
                    support_images.append(image)
                    support_labels.append(i)
            
            # 加载query图像
            for sample in query_samples:
                try:
                    image = Image.open(sample['image_path']).convert('RGB')
                    image = self.transform(image)
                    query_images.append(image)
                    query_labels.append(i)  # 使用episode内的标签
                except Exception as e:
                    logger.error(f"无法加载图像 {sample['image_path']}: {e}")
                    image = torch.randn(3, 224, 224)
                    query_images.append(image)
                    query_labels.append(i)
        
        return (torch.stack(support_images), torch.tensor(support_labels),
                torch.stack(query_images), torch.tensor(query_labels))

def prototypical_loss(query_features, support_features, support_labels, query_labels):
    """原型网络损失函数"""
    
    # 计算原型
    unique_labels = torch.unique(support_labels)
    prototypes = []
    
    for label in unique_labels:
        mask = support_labels == label
        class_features = support_features[mask]
        prototype = class_features.mean(dim=0)
        prototypes.append(prototype)
    
    prototypes = torch.stack(prototypes)
    
    # 计算查询样本到原型的距离
    distances = torch.cdist(query_features, prototypes, p=2)
    
    # 转换为logits（负距离）
    logits = -distances
    
    # 计算交叉熵损失
    loss = F.cross_entropy(logits, query_labels)
    
    # 计算准确率
    predictions = logits.argmax(dim=1)
    accuracy = (predictions == query_labels).float().mean()
    
    return loss, accuracy

def meta_train_epoch(model, dataset, optimizer, device, epoch, episodes_per_epoch=100):
    """元学习训练一个epoch"""
    
    model.train()
    
    total_loss = 0.0
    total_accuracy = 0.0
    
    for episode in range(episodes_per_epoch):
        # 采样一个episode
        support_images, support_labels, query_images, query_labels = dataset.sample_episode(
            n_way=min(8, len(dataset.classes)), k_shot=3, q_query=5
        )
        
        support_images = support_images.to(device)
        support_labels = support_labels.to(device)
        query_images = query_images.to(device)
        query_labels = query_labels.to(device)
        
        optimizer.zero_grad()
        
        # 提取特征
        support_features = model(support_images)
        query_features = model(query_images)
        
        # 计算原型损失
        loss, accuracy = prototypical_loss(query_features, support_features, 
                                         support_labels, query_labels)
        
        loss.backward()
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        optimizer.step()
        
        total_loss += loss.item()
        total_accuracy += accuracy.item()
    
    avg_loss = total_loss / episodes_per_epoch
    avg_accuracy = total_accuracy / episodes_per_epoch
    
    logger.info(f"Meta训练 Epoch {epoch+1}")
    logger.info(f"  平均损失: {avg_loss:.4f}")
    logger.info(f"  平均准确率: {avg_accuracy:.4f} ({avg_accuracy*100:.2f}%)")
    
    return avg_loss, avg_accuracy

def meta_evaluate(model, dataset, device, episodes=50):
    """元学习评估"""
    
    model.eval()
    
    total_accuracy = 0.0
    original_accuracy = 0.0
    individual_accuracy = 0.0
    original_count = 0
    individual_count = 0
    
    with torch.no_grad():
        for episode in range(episodes):
            # 采样一个episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(10, len(dataset.classes)), k_shot=2, q_query=8
            )
            
            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)
            
            # 提取特征
            support_features = model(support_images)
            query_features = model(query_images)
            
            # 计算原型并分类
            prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
            predictions, _ = model.classify_by_prototypes(query_features, prototypes, prototype_labels)
            
            # 计算准确率
            accuracy = (predictions == query_labels).float().mean()
            total_accuracy += accuracy.item()
            
            # 分别统计原始3猫和个体猫咪的准确率
            # 这里需要根据实际的类别ID来判断
            for i, (pred, true) in enumerate(zip(predictions, query_labels)):
                if true.item() < 3:  # 原始3猫
                    original_accuracy += (pred == true).float().item()
                    original_count += 1
                else:  # 个体猫咪
                    individual_accuracy += (pred == true).float().item()
                    individual_count += 1
    
    avg_accuracy = total_accuracy / episodes
    avg_original_accuracy = original_accuracy / max(original_count, 1)
    avg_individual_accuracy = individual_accuracy / max(individual_count, 1)
    
    return avg_accuracy, avg_original_accuracy, avg_individual_accuracy

def main():
    parser = argparse.ArgumentParser(description='Few-Shot Meta Learning')
    parser.add_argument('--annotations', required=True, help='标注文件')
    parser.add_argument('--individual-data-dir', required=True, help='个体数据目录')
    parser.add_argument('--original-dir', required=True, help='原始图像目录')
    parser.add_argument('--pretrained', required=True, help='预训练模型')
    parser.add_argument('--output', default='few_shot_meta_model.pth', help='输出模型')
    parser.add_argument('--max-individual-cats', type=int, default=30, help='最大个体猫咪数量')
    parser.add_argument('--epochs', type=int, default=200, help='训练轮数')
    parser.add_argument('--episodes-per-epoch', type=int, default=100, help='每轮episode数量')
    parser.add_argument('--lr', type=float, default=1e-4, help='学习率')
    
    args = parser.parse_args()
    
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"使用设备: {device}")
    
    # 数据准备
    with open(args.annotations, 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建Few-Shot数据集
    dataset = FewShotDataset(
        annotations, args.individual_data_dir, args.original_dir, 
        cat_to_id, args.max_individual_cats, True
    )
    
    # 加载预训练模型
    checkpoint = torch.load(args.pretrained, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    
    # 创建基础特征提取器
    if 'feature_extractor.backbone.patch_embed.proj.weight' in checkpoint['model_state_dict']:
        logger.info("从对比学习模型中提取特征提取器...")
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_state_dict = {}
        for key, value in checkpoint['model_state_dict'].items():
            if key.startswith('feature_extractor.'):
                new_key = key.replace('feature_extractor.', '')
                base_state_dict[new_key] = value
        base_model.load_state_dict(base_state_dict)
    else:
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        base_model.load_state_dict(checkpoint['model_state_dict'])
    
    # 创建原型网络
    model = PrototypicalNetwork(base_model, feature_dim)
    model = model.to(device)
    
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    logger.info(f"可训练参数: {trainable_params:,} / {total_params:,}")
    
    # 优化器
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # 元学习训练
    best_accuracy = 0.0
    patience = 30
    patience_counter = 0
    
    logger.info("开始Few-Shot元学习训练...")
    
    for epoch in range(args.epochs):
        # 训练
        train_loss, train_acc = meta_train_epoch(
            model, dataset, optimizer, device, epoch, args.episodes_per_epoch
        )
        
        scheduler.step()
        
        # 每10轮评估一次
        if (epoch + 1) % 10 == 0:
            val_acc, original_acc, individual_acc = meta_evaluate(model, dataset, device)
            
            logger.info(f"  验证结果 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
            logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
            
            # 早停和模型保存
            if individual_acc > best_accuracy:
                best_accuracy = individual_acc
                patience_counter = 0
                
                # 保存最佳模型
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'feature_dim': feature_dim,
                    'epoch': epoch,
                    'val_acc': val_acc,
                    'original_acc': original_acc,
                    'individual_acc': individual_acc
                }, 'best_few_shot_meta_model.pth')
                
                logger.info(f"  ✅ 新的最佳Few-Shot模型 (个体准确率: {individual_acc:.4f})")
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info(f"早停触发 (连续{patience}轮无改进)")
                    break
    
    logger.info(f"Few-Shot元学习训练完成！最佳个体准确率: {best_accuracy:.4f}")
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'feature_dim': feature_dim,
        'cat_to_id': cat_to_id
    }, args.output)
    
    logger.info(f"Few-Shot元学习模型已保存到: {args.output}")

def validate_model(model_path: str, annotations_file: str, individual_data_dir: str, original_dir: str, test_samples: int = 500):
    """验证训练好的Few-Shot Meta Learning模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")

    # 加载数据集
    # 首先创建cat_to_id映射
    with open(annotations_file, 'r', encoding='utf-8') as f:
        annotations = json.load(f)

    # 创建原始3猫的映射
    original_cats = {'小白': 0, '小花': 1, '小黑': 2}
    cat_to_id = original_cats.copy()

    # 添加个体猫的映射
    individual_dirs = [d for d in os.listdir(individual_data_dir) if os.path.isdir(os.path.join(individual_data_dir, d))]
    for i, cat_dir in enumerate(sorted(individual_dirs)):
        if cat_dir not in cat_to_id:
            cat_to_id[cat_dir] = len(cat_to_id)

    dataset = FewShotDataset(annotations, individual_data_dir, original_dir, cat_to_id)
    print(f"📊 数据集: {len(cat_to_id)} 个类别")

    # 加载模型
    checkpoint = torch.load(model_path, map_location=device, weights_only=False)
    feature_dim = checkpoint.get('feature_dim', 2048)
    num_classes = checkpoint.get('num_classes', len(cat_to_id))

    # 创建基础特征提取器
    from feature_based_cat_recognition import FeatureExtractorModel
    base_model = FeatureExtractorModel(feature_dim=feature_dim)

    # 创建原型网络
    model = PrototypicalNetwork(base_model, feature_dim=feature_dim)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.to(device)
    model.eval()

    print(f"✅ 模型加载成功: {model_path}")

    # 使用Few-Shot评估方式
    print(f"📊 开始Few-Shot评估，测试{test_samples//50}个episodes")

    total_accuracy = 0.0
    original_correct = 0
    individual_correct = 0
    original_total = 0
    individual_total = 0

    episodes = test_samples // 50  # 每个episode约50个样本

    with torch.no_grad():
        for episode in range(episodes):
            # 采样一个episode
            support_images, support_labels, query_images, query_labels = dataset.sample_episode(
                n_way=min(10, len(dataset.classes)), k_shot=2, q_query=8
            )

            support_images = support_images.to(device)
            support_labels = support_labels.to(device)
            query_images = query_images.to(device)
            query_labels = query_labels.to(device)

            # 提取特征
            support_features = model(support_images)
            query_features = model(query_images)

            # 计算原型并分类
            prototypes, prototype_labels = model.compute_prototypes(support_features, support_labels)
            predictions, _ = model.classify_by_prototypes(query_features, prototypes, prototype_labels)

            # 计算准确率
            accuracy = (predictions == query_labels).float().mean()
            total_accuracy += accuracy.item()

            # 分别统计原始3猫和个体猫的准确率
            for j, label in enumerate(query_labels):
                if label.item() < 3:  # 原始3猫
                    original_total += 1
                    if predictions[j] == label:
                        original_correct += 1
                else:  # 个体猫
                    individual_total += 1
                    if predictions[j] == label:
                        individual_correct += 1

    # 计算准确率
    total_accuracy = total_accuracy / episodes
    original_accuracy = original_correct / original_total if original_total > 0 else 0
    individual_accuracy = individual_correct / individual_total if individual_total > 0 else 0

    print(f"\n🎯 模型性能验证结果:")
    print(f"   总体准确率: {total_accuracy:.4f} ({total_accuracy*100:.2f}%)")
    print(f"   原始3猫准确率: {original_accuracy:.4f} ({original_accuracy*100:.2f}%)")
    print(f"   个体猫准确率: {individual_accuracy:.4f} ({individual_accuracy*100:.2f}%)")
    print(f"   原始3猫样本数: {original_total}")
    print(f"   个体猫样本数: {individual_total}")

    # 判断是否达到目标
    target_achieved = individual_accuracy >= 0.95 and original_accuracy >= 0.96
    print(f"\n{'✅' if target_achieved else '❌'} 目标达成: {'是' if target_achieved else '否'}")
    print(f"   个体猫准确率 >= 95%: {'✅' if individual_accuracy >= 0.95 else '❌'}")
    print(f"   原始3猫准确率 >= 96%: {'✅' if original_accuracy >= 0.96 else '❌'}")

    return {
        'total_accuracy': total_accuracy,
        'original_accuracy': original_accuracy,
        'individual_accuracy': individual_accuracy,
        'target_achieved': target_achieved
    }

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == 'validate':
        # 验证模式
        model_path = 'best_few_shot_meta_model.pth'
        annotations_file = '/home/<USER>/animsi/caby_training/tagging/annotations.json'
        individual_data_dir = '/home/<USER>/animsi/caby_training/dataset/cat_individual_images'
        original_dir = '/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails'

        if os.path.exists(model_path):
            validate_model(model_path, annotations_file, individual_data_dir, original_dir)
        else:
            print(f"❌ 模型文件不存在: {model_path}")
    else:
        main()
