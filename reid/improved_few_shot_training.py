#!/usr/bin/env python3
"""
改进的Few-Shot Meta Learning训练
专门解决原始3猫准确率下降问题，同时提升个体猫识别准确率

改进策略:
1. 平衡采样 - 确保原始3猫和个体猫的训练平衡
2. 动态早停 - 监控原始3猫准确率，防止下降
3. 学习率调度 - 使用余弦退火调度
4. 权重平衡 - 给原始3猫更高的权重
"""

import os
import sys
import json
import random
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from few_shot_meta_learning import PrototypicalNetwork, FewShotDataset, meta_train_epoch, meta_evaluate
from feature_based_cat_recognition import FeatureExtractorModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedFewShotTrainer:
    """改进的Few-Shot训练器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 训练状态
        self.best_individual_acc = 0.0
        self.best_original_acc = 0.0
        self.best_balanced_score = 0.0
        self.patience_counter = 0
        self.original_acc_decline_counter = 0
        
        # 早停参数
        self.patience = 15
        self.original_decline_tolerance = 3  # 允许原始准确率下降的轮数
        self.min_original_acc = 0.95  # 原始3猫最低准确率阈值
        
    def balanced_score(self, original_acc: float, individual_acc: float) -> float:
        """计算平衡分数，同时考虑原始3猫和个体猫的准确率"""
        # 给原始3猫更高的权重
        return self.args.original_weight * original_acc + individual_acc
    
    def should_early_stop(self, original_acc: float, individual_acc: float) -> bool:
        """判断是否应该早停"""
        # 如果原始3猫准确率低于阈值，立即停止
        if original_acc < self.min_original_acc:
            logger.warning(f"原始3猫准确率过低 ({original_acc:.4f} < {self.min_original_acc}), 触发早停")
            return True
        
        # 如果原始3猫准确率连续下降，考虑停止
        if original_acc < self.best_original_acc:
            self.original_acc_decline_counter += 1
            if self.original_acc_decline_counter >= self.original_decline_tolerance:
                logger.warning(f"原始3猫准确率连续{self.original_decline_tolerance}轮下降, 触发早停")
                return True
        else:
            self.original_acc_decline_counter = 0
        
        # 平衡分数无改进的早停
        balanced_score = self.balanced_score(original_acc, individual_acc)
        if balanced_score <= self.best_balanced_score:
            self.patience_counter += 1
            if self.patience_counter >= self.patience:
                logger.info(f"平衡分数连续{self.patience}轮无改进, 触发早停")
                return True
        else:
            self.patience_counter = 0
        
        return False
    
    def update_best_scores(self, original_acc: float, individual_acc: float) -> bool:
        """更新最佳分数，返回是否有改进"""
        balanced_score = self.balanced_score(original_acc, individual_acc)
        improved = False
        
        if individual_acc > self.best_individual_acc:
            self.best_individual_acc = individual_acc
            improved = True
        
        if original_acc > self.best_original_acc:
            self.best_original_acc = original_acc
            improved = True
        
        if balanced_score > self.best_balanced_score:
            self.best_balanced_score = balanced_score
            improved = True
        
        return improved
    
    def train(self):
        """执行改进的训练"""
        logger.info("🚀 开始改进的Few-Shot Meta Learning训练...")
        
        # 加载数据
        with open(self.args.annotations, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        # 创建cat_to_id映射
        original_cats = {'小白': 0, '小花': 1, '小黑': 2}
        cat_to_id = original_cats.copy()
        
        individual_dirs = [d for d in os.listdir(self.args.individual_data_dir) 
                          if os.path.isdir(os.path.join(self.args.individual_data_dir, d))]
        for i, cat_dir in enumerate(sorted(individual_dirs)):
            if cat_dir not in cat_to_id:
                cat_to_id[cat_dir] = len(cat_to_id)
        
        # 创建数据集
        dataset = FewShotDataset(annotations, self.args.individual_data_dir, 
                               self.args.original_dir, cat_to_id, 
                               max_individual_cats=self.args.max_individual_cats)
        
        # 创建模型
        feature_dim = 2048
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        model = PrototypicalNetwork(base_model, feature_dim=feature_dim)
        
        # 加载预训练模型
        if self.args.pretrained and os.path.exists(self.args.pretrained):
            logger.info(f"加载预训练模型: {self.args.pretrained}")
            checkpoint = torch.load(self.args.pretrained, map_location=self.device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
        
        model.to(self.device)
        
        # 优化器和调度器
        optimizer = optim.AdamW(model.parameters(), lr=self.args.lr, weight_decay=1e-4)
        
        # 使用余弦退火调度器
        scheduler = CosineAnnealingLR(optimizer, T_max=self.args.epochs, eta_min=1e-6)
        
        logger.info(f"开始训练 - 总轮数: {self.args.epochs}, 每轮episodes: {self.args.episodes_per_epoch}")
        logger.info(f"原始3猫权重: {self.args.original_weight}, 最低准确率阈值: {self.min_original_acc}")
        
        # 训练循环
        for epoch in range(self.args.epochs):
            # 训练一轮
            avg_loss, avg_accuracy = meta_train_epoch(
                model, dataset, optimizer, self.device, epoch,
                episodes_per_epoch=self.args.episodes_per_epoch
            )
            
            scheduler.step()
            
            logger.info(f"Meta训练 Epoch {epoch + 1}")
            logger.info(f"  平均损失: {avg_loss:.4f}")
            logger.info(f"  平均准确率: {avg_accuracy:.4f} ({avg_accuracy*100:.2f}%)")
            
            # 每5轮评估一次（更频繁的监控）
            if (epoch + 1) % 5 == 0:
                val_acc, original_acc, individual_acc = meta_evaluate(model, dataset, self.device)
                
                logger.info(f"  验证结果 - 总体: {val_acc:.4f}, 原始: {original_acc:.4f}, 个体: {individual_acc:.4f}")
                logger.info(f"  平衡分数: {self.balanced_score(original_acc, individual_acc):.4f}")
                logger.info(f"  学习率: {scheduler.get_last_lr()[0]:.6f}")
                
                # 更新最佳分数
                improved = self.update_best_scores(original_acc, individual_acc)
                
                if improved:
                    # 保存最佳模型
                    torch.save({
                        'model_state_dict': model.state_dict(),
                        'feature_dim': feature_dim,
                        'cat_to_id': cat_to_id,
                        'epoch': epoch + 1,
                        'val_acc': val_acc,
                        'original_acc': original_acc,
                        'individual_acc': individual_acc,
                        'balanced_score': self.balanced_score(original_acc, individual_acc)
                    }, self.args.output)
                    
                    logger.info(f"  ✅ 保存改进模型 (平衡分数: {self.balanced_score(original_acc, individual_acc):.4f})")
                
                # 检查早停条件
                if self.should_early_stop(original_acc, individual_acc):
                    break
        
        logger.info(f"训练完成！")
        logger.info(f"  最佳个体准确率: {self.best_individual_acc:.4f}")
        logger.info(f"  最佳原始准确率: {self.best_original_acc:.4f}")
        logger.info(f"  最佳平衡分数: {self.best_balanced_score:.4f}")

def main():
    parser = argparse.ArgumentParser(description='改进的Few-Shot Meta Learning训练')
    parser.add_argument('--annotations', required=True, help='标注文件路径')
    parser.add_argument('--individual-data-dir', required=True, help='个体猫数据目录')
    parser.add_argument('--original-dir', required=True, help='原始猫数据目录')
    parser.add_argument('--pretrained', help='预训练模型路径')
    parser.add_argument('--output', default='improved_few_shot_model.pth', help='输出模型路径')
    parser.add_argument('--max-individual-cats', type=int, default=50, help='最大个体猫数量')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--episodes-per-epoch', type=int, default=200, help='每轮episode数量')
    parser.add_argument('--lr', type=float, default=3e-5, help='学习率')
    parser.add_argument('--original-weight', type=float, default=1.5, help='原始3猫的权重')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    # 创建训练器并开始训练
    trainer = ImprovedFewShotTrainer(args)
    trainer.train()

if __name__ == "__main__":
    main()
