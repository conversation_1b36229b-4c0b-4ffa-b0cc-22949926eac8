#!/usr/bin/env python3
"""
最终综合评估 - 对比所有优化方案的最终结果
"""

import os
import sys
import json
import random
import argparse
import time
from pathlib import Path
from typing import Dict, List, Tuple
import numpy as np

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, classification_report
from sklearn.preprocessing import StandardScaler

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from feature_based_cat_recognition import FeatureExtractorModel
from progressive_transfer_learning import ProgressiveModel, ProgressiveDataset
from contrastive_learning_enhancement import ContrastiveProgressiveModel
from knowledge_distillation_enhancement import StudentModel

def load_model_with_type_detection(model_path: str, device: torch.device):
    """智能加载模型，自动检测模型类型"""
    
    if not os.path.exists(model_path):
        print(f"⚠️  模型文件不存在: {model_path}")
        return None
    
    try:
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        feature_dim = checkpoint.get('feature_dim', 2048)
        num_classes = checkpoint.get('num_classes', 33)
        
        # 创建基础特征提取器
        base_model = FeatureExtractorModel(feature_dim=feature_dim)
        
        # 根据state_dict的键来判断模型类型
        state_dict_keys = set(checkpoint['model_state_dict'].keys())
        
        if 'multi_scale_fusion.0.0.weight' in state_dict_keys:
            # 知识蒸馏学生模型
            model = StudentModel(base_model, feature_dim, num_classes)
            model_type = "distilled"
        elif 'projection_head.0.weight' in state_dict_keys:
            # 对比学习模型
            model = ContrastiveProgressiveModel(base_model, feature_dim, num_classes)
            model_type = "contrastive"
        elif 'self_attention.in_proj_weight' in state_dict_keys:
            # 深度优化模型
            from deep_architecture_optimization import DeepOptimizedModel
            model = DeepOptimizedModel(base_model, feature_dim, num_classes)
            model_type = "deep_optimized"
        else:
            # 渐进式模型
            model = ProgressiveModel(base_model, feature_dim, num_classes)
            model_type = "progressive"
        
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(device)
        model.eval()
        
        return model, model_type, checkpoint
        
    except Exception as e:
        print(f"⚠️  加载模型失败 {model_path}: {e}")
        return None

def comprehensive_model_test(model, model_name: str, model_type: str, test_loader, device):
    """全面测试单个模型"""
    
    print(f"\n=== 测试 {model_name} ({model_type}) ===")
    
    all_features = []
    all_labels = []
    all_is_original = []
    inference_times = []
    
    with torch.no_grad():
        for images, labels, categories, is_original in test_loader:
            start_time = time.time()
            
            images = images.to(device)
            features = model(images, return_features=True)
            
            inference_time = time.time() - start_time
            inference_times.append(inference_time)
            
            all_features.append(features.cpu().numpy())
            all_labels.extend(labels.numpy())
            all_is_original.extend(is_original.numpy())
    
    if len(all_features) == 0:
        print("❌ 没有有效的测试数据")
        return None
    
    all_features = np.vstack(all_features)
    all_labels = np.array(all_labels)
    all_is_original = np.array(all_is_original)
    
    # 性能指标
    avg_inference_time = np.mean(inference_times)
    throughput = len(all_labels) / sum(inference_times)
    
    # 使用多种KNN配置进行测试
    knn_configs = [
        {'n_neighbors': 7, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 9, 'metric': 'cosine', 'weights': 'distance'},
        {'n_neighbors': 11, 'metric': 'cosine', 'weights': 'distance'},
    ]
    
    best_result = None
    best_individual_acc = 0.0
    
    for config in knn_configs:
        knn = KNeighborsClassifier(**config)
        
        # 留一法交叉验证
        predictions = []
        for i in range(len(all_features)):
            train_mask = np.ones(len(all_features), dtype=bool)
            train_mask[i] = False
            
            if train_mask.sum() > 0:
                knn.fit(all_features[train_mask], all_labels[train_mask])
                pred = knn.predict(all_features[i:i+1])
                predictions.append(pred[0])
            else:
                predictions.append(all_labels[i])
        
        predictions = np.array(predictions)
        
        # 计算准确率
        overall_acc = accuracy_score(all_labels, predictions)
        
        original_mask = all_is_original.astype(bool)
        original_acc = accuracy_score(all_labels[original_mask], predictions[original_mask]) if original_mask.sum() > 0 else 0.0
        
        individual_mask = ~original_mask
        individual_acc = accuracy_score(all_labels[individual_mask], predictions[individual_mask]) if individual_mask.sum() > 0 else 0.0
        
        # 选择个体准确率最高的配置
        if individual_acc > best_individual_acc:
            best_individual_acc = individual_acc
            best_result = {
                'overall_acc': overall_acc,
                'original_acc': original_acc,
                'individual_acc': individual_acc,
                'config': config
            }
    
    if best_result is None:
        print("❌ 测试失败")
        return None
    
    # 输出结果
    print(f"📊 最佳性能指标 (KNN配置: {best_result['config']}):")
    print(f"  总体准确率: {best_result['overall_acc']:.4f} ({best_result['overall_acc']*100:.2f}%)")
    print(f"  原始3猫准确率: {best_result['original_acc']:.4f} ({best_result['original_acc']*100:.2f}%)")
    print(f"  个体猫咪准确率: {best_result['individual_acc']:.4f} ({best_result['individual_acc']*100:.2f}%)")
    print(f"  平均推理时间: {avg_inference_time*1000:.2f} ms/batch")
    print(f"  吞吐量: {throughput:.1f} samples/sec")
    
    # 性能评级
    if best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.75:
        grade = "🏆 卓越"
    elif best_result['original_acc'] >= 0.95 and best_result['individual_acc'] >= 0.65:
        grade = "🥇 优秀"
    elif best_result['original_acc'] >= 0.90 and best_result['individual_acc'] >= 0.50:
        grade = "🥈 良好"
    elif best_result['original_acc'] >= 0.85 and best_result['individual_acc'] >= 0.35:
        grade = "🥉 一般"
    else:
        grade = "❌ 需改进"
    
    print(f"  综合评级: {grade}")
    
    # 计算提升倍数
    baseline_individual_acc = 0.1858  # 基线18.58%
    improvement_ratio = best_result['individual_acc'] / baseline_individual_acc
    print(f"  相比基线提升: {improvement_ratio:.1f}x ({(improvement_ratio-1)*100:.1f}%)")
    
    return {
        'model_name': model_name,
        'model_type': model_type,
        'overall_acc': best_result['overall_acc'],
        'original_acc': best_result['original_acc'],
        'individual_acc': best_result['individual_acc'],
        'inference_time': avg_inference_time,
        'throughput': throughput,
        'grade': grade,
        'improvement_ratio': improvement_ratio,
        'best_config': best_result['config']
    }

def final_comprehensive_evaluation():
    """最终综合评估"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 准备测试数据
    print("📁 准备测试数据...")
    
    with open('../tagging/annotations.json', 'r', encoding='utf-8') as f:
        annotations = json.load(f)
    
    cat_to_id = {'小白': 0, '小花': 1, '小黑': 2}
    
    # 创建大规模测试数据集
    test_dataset = ProgressiveDataset(
        annotations, '../dataset/cat_individual_images', '../dataset/renamed_thumbnails', 
        cat_to_id, max_individual_cats=30, is_training=False
    )
    
    # 随机采样更多测试数据
    test_indices = random.sample(range(len(test_dataset)), min(800, len(test_dataset)))
    test_subset = torch.utils.data.Subset(test_dataset, test_indices)
    test_loader = DataLoader(test_subset, batch_size=16, shuffle=False, num_workers=2)
    
    print(f"📊 测试数据: {len(test_subset)} 样本")
    
    # 待测试的模型列表
    models_to_test = [
        {
            'name': '基础渐进式模型',
            'path': 'best_progressive_model.pth'
        },
        {
            'name': '增强渐进式模型',
            'path': 'best_enhanced_progressive_model_stage3.pth'
        },
        {
            'name': '对比学习模型',
            'path': 'best_contrastive_model.pth'
        },
        {
            'name': '深度优化模型',
            'path': 'best_deep_optimized_model.pth'
        },
        {
            'name': '知识蒸馏模型',
            'path': 'best_distilled_model.pth'
        }
    ]
    
    results = []
    
    # 测试每个模型
    for model_info in models_to_test:
        model_result = load_model_with_type_detection(model_info['path'], device)
        
        if model_result is None:
            print(f"⏭️  跳过 {model_info['name']} (模型不可用)")
            continue
        
        model, model_type, checkpoint = model_result
        
        # 测试模型
        result = comprehensive_model_test(model, model_info['name'], model_type, test_loader, device)
        
        if result:
            results.append(result)
        
        # 清理内存
        del model
        torch.cuda.empty_cache()
    
    # 生成最终报告
    print("\n" + "="*100)
    print("🏆 最终综合评估报告")
    print("="*100)
    
    if not results:
        print("❌ 没有可用的测试结果")
        return
    
    # 按个体猫咪准确率排序
    results.sort(key=lambda x: x['individual_acc'], reverse=True)
    
    print(f"{'模型名称':<20} {'模型类型':<15} {'总体准确率':<12} {'原始3猫':<12} {'个体猫咪':<12} {'提升倍数':<10} {'评级':<10}")
    print("-" * 100)
    
    for result in results:
        print(f"{result['model_name']:<20} "
              f"{result['model_type']:<15} "
              f"{result['overall_acc']*100:>8.2f}%   "
              f"{result['original_acc']*100:>8.2f}%   "
              f"{result['individual_acc']*100:>8.2f}%   "
              f"{result['improvement_ratio']:>6.1f}x    "
              f"{result['grade']:<10}")
    
    # 最佳模型分析
    best_model = results[0]
    print(f"\n🎯 最佳模型: {best_model['model_name']} ({best_model['model_type']})")
    print(f"   个体猫咪准确率: {best_model['individual_acc']*100:.2f}%")
    print(f"   原始3猫准确率: {best_model['original_acc']*100:.2f}%")
    print(f"   相比基线提升: {best_model['improvement_ratio']:.1f}倍")
    print(f"   最佳KNN配置: {best_model['best_config']}")
    
    # 技术突破分析
    print(f"\n📈 技术突破分析:")
    for result in results:
        if result['individual_acc'] >= 0.70:
            print(f"   🚀 {result['model_name']}: 达到70%+准确率，技术突破显著")
        elif result['individual_acc'] >= 0.50:
            print(f"   ✅ {result['model_name']}: 达到50%+准确率，效果良好")
        elif result['individual_acc'] >= 0.35:
            print(f"   🔶 {result['model_name']}: 达到35%+准确率，有所改进")
    
    # 保存结果
    output_file = "final_comprehensive_evaluation_results.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n📄 详细结果已保存到: {output_file}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description='最终综合评估')
    parser.add_argument('--test-samples', type=int, default=800, help='测试样本数量')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    torch.manual_seed(42)
    
    print("🚀 开始最终综合评估...")
    results = final_comprehensive_evaluation()
    print("✅ 最终综合评估完成！")

if __name__ == "__main__":
    main()
